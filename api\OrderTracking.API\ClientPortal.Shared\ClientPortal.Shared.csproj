﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <UserSecretsId>e9294c5a-7db6-4e3c-b511-9c0d223424c5</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
      <PackageReference Include="CsvHelper" Version="27.2.1" />
      <PackageReference Include="Dapper.Contrib" Version="2.0.78" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.27.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
    <PackageReference Include="morelinq" Version="3.3.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="SendGrid" Version="9.28.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.3" />
    <PackageReference Include="Z.Dapper.Plus" Version="4.0.29" />
    <PackageReference Include="Google.Cloud.Firestore" Version="3.6.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Files\OneInsight-Final.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
