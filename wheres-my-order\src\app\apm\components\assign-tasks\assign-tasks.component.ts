import { animate, style, transition, trigger } from '@angular/animations';
import { Component, inject, OnInit, ViewChild } from '@angular/core';
import { collection, doc, Firestore, getDoc, setDoc } from '@angular/fire/firestore';
import { Router } from '@angular/router';
import { generateUuid } from '@azure/core-http';
import { DxSelectBoxComponent } from 'devextreme-angular';
import { ColumnButtonClickEvent, RowUpdatedEvent } from 'devextreme/ui/data_grid';
import { SelectionChangedEvent } from 'devextreme/ui/select_box';
import moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { UsersService } from '../../../shared/services';
import { allStatuses } from '../../models';
import { Asset, BusinessUnit, DataInterface, Task, User } from '../../models/data/data-interface';

@Component({
  selector: 'app-assign-tasks',
  templateUrl: './assign-tasks.component.html',
  styleUrls: ['./assign-tasks.component.scss'],
  animations: [
    trigger(
      'inOutAnimation',
      [
        transition(
          ':enter',
          [
            style({ height: 0, opacity: 0 }),
            animate('1s ease-out',
              style({ height: '100%', opacity: 1 }))
          ]
        ),
        transition(
          ':leave',
          [
            style({ height: '100%', opacity: 1 }),
            animate('1s ease-in',
              style({ height: 0, opacity: 0 }))
          ]
        )
      ]
    )
  ]
})
export class AssignTasksComponent implements OnInit {

  // General properties and injects
  @ViewChild(DxSelectBoxComponent) businessUnitSelector: DxSelectBoxComponent;
  firestore: Firestore = inject(Firestore)

  // Data lists
  businessUnits = []
  assetsList: Asset[] = []
  tasksList: Task[] = []
  users = []

  // Other arrays
  readonly availableStatus = allStatuses
  availableTypes = ["Asset Walkdown", "Internal Visual", "External Visual"]

  // State variables
  currentUser$ = this._users.currentProfile$
  currentEmail = ""

  // Loaders
  loadingBusinessUnits = true
  loadingAssets = false
  loadingTasks = false

  /* Constructors and initialization functions */

  constructor(private readonly _router: Router,
    private readonly _users: UsersService,
    private readonly _toasts: ToastrService,) {
    DataInterface.setFirestore(this.firestore)
  }

  ngOnInit(): void {

    this.currentUser$ = this._users.currentProfile$;

    // Fetch current user

    this.currentUser$.subscribe(user => {
      this.currentEmail = user.id

      // Get business units

      DataInterface.getUser(this.currentEmail).subscribe(data => {
        this.businessUnits = []
        var u = new User(data)
        var i = 0

        if (u.effectiveBusinessUnitIds != null && u.effectiveBusinessUnitIds.length > 0) {
          u.effectiveBusinessUnitIds?.forEach(b => {

            // Get business unit data

            DataInterface.getBusinessUnit(b).subscribe(c => {
              this.businessUnits.push({ id: b, name: new BusinessUnit(c).name })
              i++
              if (i >= u.effectiveBusinessUnitIds.length) {
                this.loadingBusinessUnits = false
                this.businessUnitSelector.value = this.businessUnits.at(0).id
                // This will trigger onBusinessUnitChanged and load assets
              }
            })
          })
        } else {
          this.loadingBusinessUnits = false
        }

      })
    })

    // Fetch the list of users for tasks supervisor and assigned users.

    DataInterface.getUsers().subscribe(data => {
      this.users = data.map(u => u["Email"].replaceAll("|", "."))
    })
  }

  // Trigger functions

  onBusinessUnitChanged(e: SelectionChangedEvent) {
    this.loadingAssets = true
    DataInterface.getAssets().subscribe(data => {
      this.assetsList = data.map(a => new Asset(a))
        .filter(a => a.businessUnitId == e.selectedItem.id)
      this.loadingAssets = false
    })
  }

  onAssetChanged($event) {
    this.updateTasks($event.data.workOrderIds)
  }

  onRowUpdated(e: RowUpdatedEvent) {
    var tasksReference = collection(this.firestore, "tasks")
    var docReference = doc(tasksReference, e.data.id)
    getDoc(docReference).then(task => {
      var oldValues = new Task(task.data())
      this.writeTask(oldValues, e.data)
    })
  }

  tasksResultsClicked = (e: ColumnButtonClickEvent) => {
    this._router.navigate(['apm/edit-task', e.row.data.workOrderId], {
      queryParams: { task_id: e.row.data.id }
    })
  }

  /* Refresh the datasource of tasks by querieng the collection directly */

  updateTasks(workOrders: []) {
    this.loadingTasks = true
    if (workOrders != null) {
      DataInterface.getTasksByWorkOrders(workOrders).subscribe(data => {
        this.tasksList = data.map(t => new Task(t))
        this.loadingTasks = false
      })
    } else {
      this.tasksList = []
      this.loadingTasks = false
    }
  }

  /* Helper functions to access data */

  writeTask(old: Task, current) {
    const tasksRef = collection(this.firestore, "tasks");

    if (old.taskType != current.taskType) {
      setDoc(doc(tasksRef, current.id), {
        TaskType: current.taskType
      }, { merge: true }).then(e => {
        this._toasts.success("New type is " + current.taskType, "Task Type was updated")
      }).catch(a => {
        this._toasts.error("Error", "There was a problem saving your changes, please try again.")
      })
    }

    if (old.apmNumber != current.apmNumber) {
      var newId = current.apmNumber
      var uidProperty = generateUuid().toString()

      var docEntry = {
        "Task APM Number": {
          "ValueChangeLog": {
            [uidProperty]: {
              "U": this.currentEmail != "" ? this.currentEmail : "<EMAIL>",
              "T": moment().valueOf(),
              "V": newId
            }
          }
        }
      }
      setDoc(doc(tasksRef, current.id), docEntry, { merge: true }).then((documentReference) => {
        this._toasts.success("New Task APM Number is " + current.apmNumber, "Task APM Number was updated")
      }).catch(e => {
        this._toasts.error("Error", "There was a problem saving your changes, try again.")
        console.log(e)
      });
    }

    if (old.supervisor != current.supervisor) {
      var newSupervisor = current.supervisor
      var uidProperty = generateUuid().toString()

      var supervisorDoc = {
        "TaskDetails": {
          "Supervisor": {
            "ValueChangeLog": {
              [uidProperty]: {
                "U": this.currentEmail != "" ? this.currentEmail : "<EMAIL>",
                "T": moment().valueOf(),
                "V": newSupervisor
              }
            }
          }
        }
      }
      setDoc(doc(tasksRef, current.id), supervisorDoc, { merge: true }).then((documentReference) => {
        this._toasts.success("New supervisor is " + current.supervisor, "Supervisor was updated")
      }).catch(e => {
        this._toasts.error("Error", "There was a problem saving your changes, try again.")
        console.log(e)
      });

    }

    if (!this.compareArrays(old.assignedUsers, current.assignedUsers)) {
      var assigneesDoc = {
        "AssignedUsers": current.assignedUsers
      }

      setDoc(doc(tasksRef, current.id), assigneesDoc, { merge: true }).then((documentReference) => {
        this._toasts.success("Updated field successfully", "Assigned users were updated")
      }).catch(e => {
        this._toasts.error("Error", "There was a problem saving your changes, try again.")
        console.log(e)
      });
    }

    if (old.status !== current.status) {
      var newStatus = current.status
      var uidProperty = generateUuid().toString()

      var statusDoc = {
        "Status": {
          "ValueChangeLog": {
            [uidProperty]: {
              "U": this.currentEmail != "" ? this.currentEmail : "<EMAIL>",
              "T": moment().valueOf(),
              "V": newStatus
            }
          }
        }

      }
      setDoc(doc(tasksRef, current.id), statusDoc, { merge: true }).then((documentReference) => {
        this._toasts.success("New status is " + current.status, "Status was updated")
      }).catch(e => {
        this._toasts.error("Error", "There was a problem saving your changes, try again.")
        console.log(e)
      });
    }
  }

  // Utils

  customizeText(cellInfo) {
    return cellInfo.value?.toString().replaceAll(",", ", ")
  }

  compareArrays(array1, array2): boolean {
    if (array1 === undefined) {
      array1 = []
    }

    if (array2 === undefined) {
      array2 = []
    }
    const array2Sorted = array2.slice().sort();
    return array1.length === array2.length && array1.slice().sort().every(function (value, index) {
      return value === array2Sorted[index];
    });
  }
}