﻿using ClientPortal.Shared.Models;
using Google.Cloud.Firestore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using OrderTracking.API.Extensions;

namespace OrderTracking.API.Repositories
{
    public abstract class BaseFirestoreRepository<TEntity, TKey> : IAsyncFirestoreRepository<TEntity, TKey>
        where TEntity : IFirestoreEntity<TKey>
    {
        protected FirestoreDb FirestoreDb { get; }
        protected CollectionReference Collection { get; }

        protected BaseFirestoreRepository(FirestoreDb firestoreDb)
        {
            FirestoreDb = firestoreDb ?? throw new ArgumentNullException(nameof(firestoreDb));
            Collection = FirestoreDb.Collection(typeof(TEntity).Name.PascalToKebabPlural());
        }

        public virtual async Task<TEntity> AddAsync(TEntity entity)
        {
            try
            {      
                var documentReference = await Collection.AddAsync(entity);
                var documentSnapshot = await documentReference.GetSnapshotAsync();
                return documentSnapshot.ConvertTo<TEntity>();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
        {
            try
            {                
                var querySnapshot = await Collection.GetSnapshotAsync();
                return querySnapshot.Documents.Select(doc => doc.ConvertTo<TEntity>());
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public abstract Task<TEntity> GetAsync(TKey entityId);

        public virtual async Task<TEntity> GetAsync(TKey id, string partitionId)
        {
            try
            {
                var documentSnapshot = await Collection.Document(id.ToString()).GetSnapshotAsync();
                return documentSnapshot.Exists ? documentSnapshot.ConvertTo<TEntity>() : default;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task RemoveAsync(TKey id)
        {
            try
            {               
                await Collection.Document(id.ToString()).DeleteAsync();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public abstract Task RemoveAsync(TKey id, string partitionId);

        public virtual async Task RemoveAsync(TEntity entity)
        {
            try
            {
                await RemoveAsync(entity.Id);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public virtual async Task<TEntity> UpdateAsync(TEntity entity, TEntity originalEntity)
        {
            try
            {
                await Collection.Document(entity.Id.ToString()).SetAsync(entity);
                return entity;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public abstract Task<TEntity> UpdateAsync(TEntity entity, TKey originalId);

        public async Task<TEntity> UpdateAsync(TEntity entity)
        {
            try
            {
                await Collection.Document(entity.Id.ToString()).SetAsync(entity);
                return entity;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }
    }
}
