﻿using System;
using Newtonsoft.Json;
using Google.Cloud.Firestore;

namespace ClientPortal.Shared.Models
{
    [FirestoreData]
    public class ChangeEvent: IFirestoreEntity<string>
    {
        [FirestoreProperty("id")]
        [JsonProperty("id")]
        public string Id { get; set; }

        [FirestoreProperty("old")]
        [JsonProperty("old")]
        public object Old { get; set; }

        [FirestoreProperty("new")]
        [JsonProperty("new")]
        public object New { get; set; }

        [FirestoreProperty("user")]
        [JsonProperty("user")]
        public object User { get; set; }

        [FirestoreProperty("createdAt")]
        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; }
    }
}