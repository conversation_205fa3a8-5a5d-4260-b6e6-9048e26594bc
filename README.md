# Kraken

This project consists of a .NET 6 ASP.NET Core Web API, located in the `api` folder, as well as an Angular 14 client application, located in the [wheres-my-order](/wheres-my-order/README.md) folder.

## 🚀 Azure Migration Complete

**This project has been successfully migrated from Google Cloud Platform (GCP) to Microsoft Azure.**

**✅ Migration Status: 100% COMPLETE** - All GCP dependencies removed and replaced with Azure equivalents.

### Key Changes:
- **Container Platform**: Google Cloud Run → Azure Container Apps
- **Storage**: Google Cloud Storage → Azure Blob Storage
- **Secrets Management**: Google Secret Manager → Azure Key Vault
- **Database**: Google Cloud Firestore → Azure Cosmos DB
- **Authentication**: Firebase Auth → Azure AD B2C
- **CI/CD**: Google Cloud Build → Azure DevOps Pipelines
- **Container Registry**: Google Artifact Registry → Azure Container Registry
- **Monitoring**: Google Cloud Diagnostics → Azure Application Insights

### Migration Documentation:
- 📋 **[Migration Guide](MIGRATION-README.md)** - Comprehensive migration documentation
- ✅ **[Validation Checklist](MIGRATION-VALIDATION-CHECKLIST.md)** - Post-migration validation steps
- 🧪 **[Testing Guide](scripts/Run-MigrationTests.ps1)** - Automated testing scripts
- 🧹 **[GCP Cleanup Report](GCP-CLEANUP-REPORT.md)** - Detailed report of all GCP references removed
- 🔥 **[Firebase Model Cleanup Report](FIREBASE-MODEL-CLEANUP-REPORT.md)** - Firebase model migration details

### Quick Start (Azure):
```bash
# Deploy infrastructure
az deployment group create --resource-group rg-kraken-dev --template-file infrastructure/main.bicep

# Run validation tests
./scripts/Validate-AzureMigration.ps1 -Environment dev

# Deploy applications
az containerapp update --name ca-cpa-backend-dev --resource-group rg-kraken-dev --image krakenacr.azurecr.io/cpa-backend:latest
```

## Developing on Ubuntu

Run `dotnet dev-certs https`. Before attempting to access API resources through a client application, make sure you go to `https://localhost:5001/api/<resource>` and make an exception for the certificate. This is a known issue on Ubuntu for development certs.

In Chrome, the page usually gives a warning about not trusting the certificate and that the site might not be safe. There is an `Advanced` link lower on the page that gives you the option of proceeding to the url anyway. This seems to add the exception to chrome for our API running locally in development on an Ubuntu based operating system.

## Background tasks

Background tasks are complicated. The topic is broad, the patterns are plenty, and the architectural considerations are nuanced. But knowing that we'll need to be able to take advantage of background tasks in our application, how do we choose the right approach for the team?

### Why?

Why do we need background tasks? Sometimes, it is easier to start with a known use-case and work our way towards a general statement.

Hypothetically, when a user signs up or registers for a site for the first time, an email is sent to that user to help confirm that the email address provided during registration is a valid one and one that the end user has access to. This also helps the application have confidence that future communication/notifications that involves email will reach the user.

When that user clicks the "Register" button, the user is generally not expected to sit and wait for the application to send the request to the server-side application, spin up some email service, look up the current environment's email configuration, build an email through some template, send the email, and then finally respond to the client-side application to let the user know something has finally happened. Instead, the client-side application usually will quickly tell the user that their request has been made and that they can expect an email in the inbox "pretty soon." The client-side application isn't going to sit and wait for all that to happen, but it is all still going to happen, in the **background**.

Sometimes, workflows aligned with business need will be triggered by certain events that take place during normal usage of the application we are building. These workflows can be doing quite a bit of work, or communicating with multiple, separate, back-end systems, and don't require all that coordination to be communicated to the end-user. We need a way to implement these features while working in a familiar environment, and hopefully we can continue using the tools that are helping us implement the rest of the application (dependency injection, etc.).

### Background tasks with hosted services in ASP.NET Core

At the time of this writing, we are using dotnet core 3.1 to write a web api that an Angular CLI application uses to retrieve data from Cosmos DB. The web api and the Angular CLI application are both being built and deployed in a docker container into the Azure Kubernetes Services (AKS).

ASP.NET Core 3.1 offers a solution for background tasks. It is referred to as "background tasks with hosted services in ASP.NET Core." These can be triggered in various ways, behave like a job queue, behave like a scheduled job, etc. Some benefits of using these are:

- There are a lot of templates and examples to gain inspiration from.
- Can used scoped services with dependency injection

### Azure Function / Azure WebJob

This is easier to deploy, as the deployment is integrated into the IDE you are probably using (Visual Studio or Visual Studio Code). This follows our desired architecture.

One disadvantage is that Azure Functions have a maximum execution time. The default is 5 minutes, but it can be further configured to have a maximum execution time of 10 minutes. If we needed something longer, we'd need to either go for the premium plan or go with something else. If our process takes longer than the maximum execution time, we should expect the execution to be shut down, whether we are done or not.

### [Deployment](https://docs.microsoft.com/en-us/dotnet/architecture/microservices/multi-container-microservice-net-applications/background-tasks-with-ihostedservice)

- "[If] you deploy your `WebHost` on IIS or a regular Azure App Service, your host can be shut down because of app pool recycles.
- "But if you are deploying your host as a container into an orchestrator like kubernetes or Service Fabric, you can control the assured number of live instances of your host.
- "[You] could consider other approaches in the cloud especially made for these scenarios, like Azure Functions.
- "[If] you need the service to be running all the time and are deploying on a Windows Server you could use a Windows Service."

Essentially, this option can be deployed in a lot of different ways. This is a separate executable, so it can be in a separate docker container and deployed to the same kubernetes cluster so that we can control the number of instances of this background job relative to the number of instances of the API. We can also easily make it a Windows Service to be run in a full Windows Server virtual machine, if the need arises.

#### Azure Function / Azure WebJob

- You can use WebJobs to execute custom jobs based on a range of different types of scripts or executable programs within the context of a web app.
- To minimize the impact of jobs on the performance of the web app, consider creating an empty Azure Web App instance in a new App Service plan to host long-running or resource-intensive WebJobs.

##### Best practices according to https://docs.microsoft.com/en-us/azure/azure-functions/functions-best-practices

- Avoid long running functions
  - Whenever possible, refactor large functions into smaller function sets that work together and return responses fast.
- Cross function communication
  - Durable Functions and Azure Logic Apps are built to manage state transitions and communication between multiple functions.
- Write functions to be stateless
- Write defensive functions
  - Assume your function could encounter an exception at any time. Design your functions with the ability to continue from a previous fail point during the next execution.
- More [here](https://docs.microsoft.com/en-us/azure/azure-functions/functions-best-practices)

#### Azure Virtual Machines

- If you have a Windows service or want to use the Windows Task Scheduler, it is common to host your background tasks within a dedicated virtual machine.
- Hosting background tasks in a separate Azure virtual machine provides flexibility and allows precise control over initialization, execution, scheduling, and resource allocation. However, it will increase runtime cost if a virtual machine must be deployed just to run background tasks.
- Nothing built in to monitor the process(es). Not impossible, just nothing built in.

#### Azure Batch

- Batch is a platform service that schedules compute-intensive work to run on a managed collection of virtual machines. It can automatically scale compute resources.
- Consider Azure Batch if you need to run large, parallel high-performance computing (HPC) workloads across, tens, hundreds, or thousands of VMs.

#### Azure Kubernetes Service (AKS)

- Azure Kubernetes Service provides a managed hosting environment for Kubernetes on Azure.
- Containers support high-density hosting. You can isolate a background task in a container, while placing multiple containers in each VM.
- The container orchestrator handles internal load balancing, configuring the internal network, and other configuration tasks.
- Containers can be started and stopped as needed.
- Azure Container Registry allows you to register your containers inside Azure boundaries. This comes with security, privacy, and proximity benefits.
- Requires an understanding of how to use a container orchestrator. Depending on the skillset of your DevOps team, this may or may not be an issue.

## Cost considerations:

- At the time of writing, we already have a kubernetes cluster to deploy to (1 environment so far).
  - We would need to investigate further how to control the number of instances for various containers (maybe only want exactly one of these running at any given time). What effects our cost in terms of adding more containers to our existing cluster?
- Azure Functions / Azure WebJobs are going to be slightly more expensive, I'm guessing, because of everything they are taking care of for you. But it might be worth it if the development efforts are easier.
- We currently aren't spinning up a full Windows Server virtual machine and running this through IIS (not saying we won't head that direction). If we do head down that direction, we might want to consider deploying our background job as a Windows Service instead because that will help enforce only one instance running for the environment as well as the fact that it won't incur any additional cost. But if we don't have a Windows Server virtual machine ahead of time, it isn't worth spinning one up just for this service.

## QA-automation:

- Before running the scripts open the cypress.env file and enter the username. passwords and Assignees respectively. Enter the APM username under the name mentioned as APM and client username under the name mentioned as CLient username.Also please try to change all the information in the Fixture folder before running it every single time.
- Run the scripts using "npx cypress open".
- It navigates to the next page click "E2E Testing".
- Then click "Start E2E Testing in chrome" and click your spec to run the scripts.
- Before running the APM test scripts install an Authenticator app to bypass the MFA.
- Log in to https://login.microsoftonline.com/ with your SSO credentials.
- After logging in, click on your Profile icon and select “View Account.”
- On the account info page, choose “Security info.” Since MFA is enabled, you’ll see options like SMS and Authenticator app-based OTP methods.
- Click on “+ Add sign-in method.”
- Select “Authenticator app” from the dropdown menu and click “Add.”
- A dialog box will prompt you to install the Microsoft Authenticator app on your mobile device (you can use other authenticator apps like Google Authenticator).
- Depending on your app, click “Next” or choose the option “I want to use a different authenticator app.”
- In the subsequent dialog, click “Next” to set up your account.
- A dialog with a QR code will appear. Look for the button below the QR code that says “Can’t scan image?”
- Click on “Can’t scan image?” to reveal your account name along with your email address and the secret key. Copy and save this information for later use.
- Use your mobile app to scan the QR code. It will set up your account, and new codes will start appearing in your mobile app.
- In the browser, you’ll be prompted to enter the 6-digit code from the newly added account in your Authenticator app. Enter the code and click “Next.”
- Your new Authenticator app method setup is complete. Ensure you safeguard the secret key obtained in the previous step.
