﻿using Google.Apis.Auth.OAuth2;
using Google.Cloud.Storage.V1;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderTracking.API.Helpers;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace OrderTracking.API.Services
{
    /// <summary>
    /// Cloud Storage Service
    /// This class contains operations related list. add, download, upload and delete objects
    /// </summary>
    public abstract class CloudStorageService : ICloudStorageService
    {
        private readonly ILogger _logger;
        private readonly IOptions<BlobStorage> _options;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="options"></param>
        /// <param name="logger"></param>
        public CloudStorageService(IOptions<BlobStorage> options, ILogger logger)
        {
            _logger = logger;
            _options = options;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetSignedUrl()
        {

            string projectId = _options.Value.APMCloudStorageProjectId;
            string secretId = _options.Value.APMServiceCredentialsSecretId;
            string bucketName = _options.Value.APMCloudStorageBucketName;
            string serviceAccountJson = CloudStorageServiceHelper.GetSecret(projectId, secretId);
            GoogleCredential credential = GoogleCredential.FromJson(serviceAccountJson);
            UrlSigner urlSigner = UrlSigner.FromCredential(credential.UnderlyingCredential as ServiceAccountCredential);
            _logger.LogInformation($"Generate signed Service Account Signed URL for {bucketName}");
            return await urlSigner.SignAsync(bucketName, null, TimeSpan.FromHours(1));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        public async Task<string> GetSignedUrl(string objectName)
        {

            string projectId = _options.Value.APMCloudStorageProjectId;
            string secretId = _options.Value.APMServiceCredentialsSecretId;
            string serviceAccountJson = CloudStorageServiceHelper.GetSecret(projectId, secretId);
            string bucketName = _options.Value.APMCloudStorageBucketName;
            GoogleCredential credential = GoogleCredential.FromJson(serviceAccountJson);
            UrlSigner urlSigner = UrlSigner.FromCredential(credential.UnderlyingCredential as ServiceAccountCredential);
            _logger.LogInformation($"Generate signed Service Account Signed URL for bucket - {bucketName} and object - {objectName}");
            return await urlSigner.SignAsync(bucketName, objectName, TimeSpan.FromHours(1));
        }

        /// <summary>
        /// 
        /// </summary>.
        /// <param name="bucketName"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<IEnumerable<Google.Apis.Storage.v1.Data.Object>> ListObjectAsync(string folderName)
        {
            try
            {
                using var storage = StorageClient.Create();
                List<Google.Apis.Storage.v1.Data.Object> objects = new List<Google.Apis.Storage.v1.Data.Object>();
                var storageObjects = storage.ListObjectsAsync(_options.Value.APMWOCloudStorageBucketName, folderName + "/");
                _logger.LogInformation($"Files in bucket {_options.Value.APMWOCloudStorageBucketName}, folder - {folderName}");
                await storageObjects.ForEachAsync((storageObject) =>
                {
                    objects.Add(storageObject);
                    Console.WriteLine(storageObject.Name);
                });
                return objects;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occured while listing files from the google cloud storage");
                throw;
            }
            
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="file/param>
        /// <param name="uploadedByEmail"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<Google.Apis.Storage.v1.Data.Object> UploadAttachmentObjectAsync(string folderName, IFormFile file)
        {
            try
            {
                if (file == null) throw new ArgumentNullException(nameof(file));
                using var storageClient = StorageClient.Create();
                using var fileStream = file.OpenReadStream();
                _logger.LogInformation($"Upload Started for bucket {_options.Value.APMWOCloudStorageBucketName}, folder - {folderName}");
                return await storageClient.UploadObjectAsync(_options.Value.APMWOCloudStorageBucketName, folderName + "/" + Uri.EscapeDataString(file.FileName), null, fileStream);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occured while uploading file {file.Name} to the google cloud storage");
                throw;
            }                      
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task DeleteObjectAsync(string folderName, string objectName)
        {
            try
            {
                using var storage = StorageClient.Create();
                _logger.LogInformation($"Deleting {objectName} from bucket {_options.Value.APMWOCloudStorageBucketName}, folder - {folderName}");
                await storage.DeleteObjectAsync(_options.Value.APMWOCloudStorageBucketName, folderName + "/" + Uri.EscapeDataString(objectName));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occured while deleting file {objectName} from the google cloud storage");
                throw;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<CloudStorageDownloadedObject> DownloadObjectAsync(string folderName, string objectName)
        {
            try
            {
                using var storage = StorageClient.Create();
                Stream stream = new MemoryStream();
                CloudStorageDownloadedObject downloadedObject = new();
                _logger.LogInformation($"Downloading {objectName} from bucket {_options.Value.APMWOCloudStorageBucketName}");
                downloadedObject.Object = await storage.DownloadObjectAsync(_options.Value.APMWOCloudStorageBucketName, folderName + "/" + Uri.EscapeDataString(objectName), stream);
                downloadedObject.Stream = stream;

                if (stream.CanSeek)
                {
                    stream.Seek(0, SeekOrigin.Begin);
                }

                return downloadedObject;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error occured while download file {objectName} from the google cloud storage");
                throw;
            }            
        }

    }
}
