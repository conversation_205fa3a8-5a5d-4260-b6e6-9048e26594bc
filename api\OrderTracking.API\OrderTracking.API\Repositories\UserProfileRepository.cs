﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Google.Cloud.Firestore;
using Microsoft.Extensions.Configuration;
using OrderTracking.API.Extensions;

namespace OrderTracking.API.Repositories
{
    public class UserProfileRepository : BaseFirestoreRepository<UserProfile, string>, IUserProfileRepository, IFirestoreRepository
    {
        private string _partitionProperty;
        #region Constructors

        public UserProfileRepository(IContainerFactory containerFactory, IConfiguration configuration) : base(CreateFirestoreDb(configuration))
        {
            CollectionReference collectionReference = containerFactory.CreateCollection<UserProfileRepository>(out var partitionPropertyPath);
            SetPartitionPropertyFromPath(partitionPropertyPath);
        }

        private void SetPartitionPropertyFromPath(string path)
        {
            if (path == null) throw new ArgumentNullException(nameof(path));

            // Split the path and set partition property based on some logic
            // Example:
            var pathSegments = path.Split('/');
            _partitionProperty = pathSegments.LastOrDefault();
        }

        public UserProfileRepository(FirestoreDb container)
            : base(container)
        {
        }

        #endregion

        #region Interface Implementation

        public override async Task<UserProfile> GetAsync(string entityId)
        {
            // Call the base class method to get the document snapshot
            var documentSnapshot = await GetAsync(entityId, entityId);
            return documentSnapshot;
        }

        public override async Task<UserProfile> AddAsync(UserProfile entity)
        {
            try
            {
                DocumentReference docRef = FirestoreDb.Collection(base.Collection.Id).Document(entity.Id.ToString());
                WriteResult writeResult = await docRef.SetAsync(entity);
                Console.WriteLine(writeResult.UpdateTime);

                return await GetAsync(entity.Id);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        public async Task<IEnumerable<UserProfile>> GetUsersForGroupAsync(string group)
        {
            var collection = FirestoreDb.Collection(typeof(UserProfile).Name);

            // Fetch all UserProfile documents
            var userProfileQuerySnapshot = await collection.GetSnapshotAsync();

            // Filter the documents based on the roles array
            var filteredUserProfiles = new List<UserProfile>();
            foreach (var documentSnapshot in userProfileQuerySnapshot)
            {
                if (!documentSnapshot.Exists)
                    continue;

                var userProfile = documentSnapshot.ConvertTo<UserProfile>();
                var roles = userProfile.Roles ?? new List<string>();

                // Check if any role starts with the specified group
                if (roles.Any(role => role.ToUpper().StartsWith(group.ToUpper())))
                    filteredUserProfiles.Add(userProfile);
            }

            return filteredUserProfiles;
        }

        public async Task<IEnumerable<UserProfile>> GetUsersForRoleAsync(string role)
        {
            var collection = FirestoreDb.Collection(typeof(UserProfile).Name.PascalToKebabPlural());

            // Fetch all UserProfile documents
            var userProfileQuerySnapshot = await collection.GetSnapshotAsync();

            // Filter the documents based on the roles array
            var filteredUserProfiles = new List<UserProfile>();
            foreach (var documentSnapshot in userProfileQuerySnapshot)
            {
                if (!documentSnapshot.Exists)
                    continue;

                var userProfile = documentSnapshot.ConvertTo<UserProfile>();
                var roles = userProfile.Roles ?? new List<string>();

                // Check if the role array contains the specified role
                if (roles.Contains(role))
                    filteredUserProfiles.Add(userProfile);
            }

            return filteredUserProfiles;
        }

        public override async Task RemoveAsync(string id)
        {
            await base.RemoveAsync(id);
        }

        public override Task RemoveAsync(string id, string partitionId)
        {
            throw new System.NotImplementedException();
        }

        public override async Task<UserProfile> UpdateAsync(UserProfile profile, string originalId)
        {
            var collection = FirestoreDb.Collection(typeof(UserProfile).Name.PascalToKebabPlural());
            try
            {
                await collection.Document(originalId).SetAsync(profile);
                return profile;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        #endregion

        #region Private Methods

        private async Task<IEnumerable<UserProfile>> GetResultsFromQuery(QuerySnapshot querySnapshot)
        {
            var results = new List<UserProfile>();
            foreach (var documentSnapshot in querySnapshot.Documents)
            {
                results.Add(documentSnapshot.ConvertTo<UserProfile>());
            }
            return results;
        }

        private static FirestoreDb CreateFirestoreDb(IConfiguration configuration)
        {
            // Implement logic to create and return FirestoreDb instance using the containerFactory
            FirestoreDb firestoreDb = new FirestoreDbBuilder
            {
                ProjectId = configuration["Connections:ProjectId"],
                DatabaseId = configuration["Connections:DatabaseName"]
            }.Build();

            return firestoreDb;
        }


        #endregion
    }
}