﻿using Google.Cloud.Firestore;
using Microsoft.Azure.Cosmos;

namespace OrderTracking.API.Repositories
{
    /// <summary>
    ///     Container Factory interface
    /// </summary>
    public interface IContainerFactory
    {
        #region Public Methods

        /// <summary>
        ///     Create Firestore collection instance
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="partitionKeyPath"></param>
        /// <returns></returns>
        CollectionReference CreateCollection<T>(out string partitionKeyPath) where T : IFirestoreRepository;

        #endregion
    }
}