using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Google.Cloud.Firestore;
using Microsoft.Extensions.Options;
using OrderTracking.API.Extensions;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     CosmosDB-based service class to CRUD release notes documents
    /// </summary>
    public class ReleaseNotesService : IReleaseNotesService
    {
        private readonly CollectionReference _collection;

        /// <summary>
        ///     Constructor for the class
        /// </summary>
        /// <param name="client"></param>
        /// <param name="options"></param>
        public ReleaseNotesService(IFirestoreClientAdapter adapter ,IOptions<Connections> options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            _collection = adapter.GetCollection(options.Value.ReleaseNotes);
        }

        /// <summary>
        ///     Get items ordered by createdAt, desc
        /// </summary>
        /// <param name="queryString"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ReleaseNotes>> GetItemsAsync()
        {
            var querySnapshot = await _collection.GetSnapshotAsync();
            var result =  querySnapshot.Documents.Select(doc => doc.ConvertTo<ReleaseNotes>());
            return result.OrderByDescending(r => r.CreatedAt);
        }

        /// <summary>
        ///     Create a release notes document in the database.
        /// </summary>
        /// <param name="releaseNotes"></param>
        /// <returns></returns>
        public async Task<ReleaseNotes> AddItemAsync(ReleaseNotes releaseNotes)
        {
            DocumentReference document = await _collection.AddAsync(releaseNotes);
            DocumentSnapshot snapshot = await document.GetSnapshotAsync();
            return snapshot.ConvertTo<ReleaseNotes>();
        }

        /// <summary>
        ///     Delete a release notes document from the database.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task DeleteItemAsync(string id)
        {
            await _collection.Document(id).DeleteAsync();
        }

        /// <summary>
        ///     Update an existing release notes document in the database.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="releaseNotes"></param>
        /// <returns></returns>
        public async Task UpdateItemAsync(string id, ReleaseNotes releaseNotes)
        {
            if (releaseNotes == null) throw new ArgumentNullException(nameof(releaseNotes));
            await _collection.Document(id).SetAsync(releaseNotes);
        }

        /// <summary>
        ///     Get a single release notes document from the database.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ReleaseNotes> GetItemAsync(string id)
        {
            var documentSnapshot = await _collection.Document(id).GetSnapshotAsync();
            return documentSnapshot.Exists ? documentSnapshot.ConvertTo<ReleaseNotes>() : default;
        }

        /// <summary>
        ///     Delete multiple release notes items from the database at once.
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task DeleteItemsAsync(string[] ids)
        {
            if (ids == null) throw new ArgumentNullException(nameof(ids));

            foreach (var id in ids)
            {
                await _collection.Document(id).DeleteAsync();
            }
        }

    }
}