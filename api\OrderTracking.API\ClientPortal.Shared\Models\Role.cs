﻿using System;
using Google.Cloud.Firestore;
using Newtonsoft.Json;

namespace ClientPortal.Shared.Models
{
    /// <summary>
    ///     Roles used for policy based authorization control
    /// </summary>
    [FirestoreData]
    public class Role : IFirestoreEntity<string>, IEquatable<Role>
    {
        /// <summary>
        ///     Default constructor for Newtonsoft deserialization
        /// </summary>
        public Role()
        {
        }

        /// <summary>
        ///     Constructs a Role based on roleKey.
        /// </summary>
        /// <param name="roleKey"></param>
        /// <param name="description"></param>
        public Role(string roleKey, string description = null)
        {
            if (roleKey == null) throw new ArgumentNullException(nameof(roleKey));

            var parts = roleKey.Split(':', '.');
            if (parts.Length == 2)
            {
                Group = parts[0];
                RoleName = parts[1];
                Description = description;
                RoleKey = $"{Group}:{RoleName}";
            }
            else
            {
                throw new InvalidOperationException(
                    $"Supplied roleKey ('{roleKey}') does not match format 'group:role' or 'group.role'");
            }
        }

        /// <summary>
        ///     Constructs a Role based on group and role, which together create a role key of the form `group:role`
        /// </summary>
        /// <param name="group"></param>
        /// <param name="role"></param>
        /// <param name="description"></param>
        public Role(string group, string role, string description = null)
        {
            Group = group;
            RoleName = role;
            Description = description;
            RoleKey = $"{Group}:{RoleName}";
        }

        #region Properties

        /// <summary>
        ///     Additional description of the purpose of the role.
        /// </summary>
        /// <value></value>
        [JsonProperty(PropertyName = "description")]
        [FirestoreProperty("description")]
        public string Description { get; set; }

        /// <summary>
        ///     Role Key prefix
        /// </summary>
        /// <value></value>
        [JsonProperty(PropertyName = "group")]
        [FirestoreProperty("group")]
        public string Group { get; set; }

        /// <summary>
        ///     Id maps to <see cref="RoleKey" />
        /// </summary>
        [JsonIgnore]
        [FirestoreProperty("id")]
        public string Id => RoleKey;

        /// <summary>
        ///     RoleKey is the combination of Group and Role, i.e. `WMO:Admin` where
        ///     the group is `WMO` and the role is `Admin`
        /// </summary>
        /// <value></value>
        [JsonProperty(PropertyName = "id")]
        [FirestoreDocumentId]
        public string RoleKey { get; set; }

        /// <summary>
        ///     The name of the role.
        /// </summary>
        /// <value></value>
        [JsonProperty(PropertyName = "roleName")]
        [FirestoreProperty("roleName")]
        public string RoleName { get; set; }

        #endregion

        #region Public Methods

        /// <summary>
        ///     Equality checker.
        /// </summary>
        /// <param name="other"></param>
        /// <returns></returns>
        public bool Equals(Role other)
        {
            if (ReferenceEquals(null, other)) return false;
            if (ReferenceEquals(this, other)) return true;
            return Description == other.Description && RoleKey == other.RoleKey;
        }

        /// <summary>
        ///     Equals override
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj)) return false;
            if (ReferenceEquals(this, obj)) return true;
            if (obj.GetType() != GetType()) return false;
            return Equals((Role) obj);
        }

        /// <summary>
        ///     GetHashCode override to override equality checks
        /// </summary>
        /// <returns></returns>
        public override int GetHashCode() => HashCode.Combine(Description, RoleKey);

        /// <summary>
        ///     Custom ToString() method.
        /// </summary>
        /// <returns></returns>
        public sealed override string ToString() => RoleKey;

        #endregion
    }
}