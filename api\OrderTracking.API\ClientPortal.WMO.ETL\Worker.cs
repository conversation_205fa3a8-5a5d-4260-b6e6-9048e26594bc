using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Services;
using ClientPortal.WMO.ETL.Services;
using Google.Cloud.Diagnostics.Common;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Z.Dapper.Plus;

namespace ClientPortal.WMO.ETL
{
    public class Worker : IHostedService, IDisposable
    {
        private static string _environmentName;
        private readonly Emails _emails;
        private readonly IEmailService _emailService;
        private readonly IETLService _etlService;
        private readonly ILogger<Worker> _logger;
        private readonly IManagedTracer _managedTracer;
        private readonly ZDapperPlus _zDapperPlus;
        private OrdersJob _ordersJob = new OrdersJob { Id = Guid.NewGuid().ToString() };
        private bool _running;
        private int errorCount = 0;
        private const int emailThreshold = 5;

        private Timer _timer;

        public Worker(
            ILogger<Worker> logger,
            IManagedTracer managedTracer,
            IETLService etlService,
            IEmailService emailService,
            IOptions<Emails> emails,
            IOptions<ZDapperPlus> zDapperPlus
        )
        {
            _logger = logger;
            _etlService = etlService;
            _emailService = emailService;
            _zDapperPlus = zDapperPlus.Value;
            _emails = emails.Value;
        }

        public void Dispose()
        {
            _timer?.Dispose();
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Timed Hosted Service running.");
            _environmentName = Environment.GetEnvironmentVariable("ENV_NAME");

            DapperPlusManager.AddLicense(_zDapperPlus.LicenseName, _zDapperPlus.LicenseKey);

            var valid = DapperPlusManager.ValidateLicense(out var errorMessage);
            if (!valid)
                await _emailService.SendUrgentEmail(SetUpRecipients(), "Invalid ZDapperPlus License", $@"
<p>ZDapperPlus was not properly licensed for this process.  Please check app settings/configuration.</p>
<p>ErrorMessage: {errorMessage}</p>
");

            _timer = new Timer(DoWork, null, TimeSpan.Zero,
                TimeSpan.FromMinutes(60));
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Timed Hosted Service is stopping.");

            _timer?.Change(Timeout.Infinite, 0);

            await _etlService.FinishJobAsync(_ordersJob ?? _etlService.StartJob(), "Stopped");
        }

        public async void DoWork(object state)
        {
            using (_managedTracer.StartSpan("work"))
            {
                if (_running)
                {
                    await _etlService.SkipJobAsync();
                    return;
                }

                _logger.LogInformation("===== Starting ETL Job =====");

                var stopwatch = Stopwatch.StartNew();

                var ordersJob = _etlService.StartJob();
                _running = true;

                try
                {
                    await _etlService.EnsureTargetDatabaseReadyAsync();

                    await _etlService.BulkCopySourceToTargetAsync();

                    await _etlService.CheckSourceData();

                    await _etlService.InsertNewOrdersAsync(ordersJob);
                    await _etlService.UpdateChangedOrdersAsync(ordersJob);
                    await _etlService.DeleteRemovedOrdersAsync(ordersJob);

                    await _etlService.FinishJobAsync(ordersJob, "Success");
                    errorCount = 0;
                }
                catch (UndesirableDataException ude)
                {
                    _logger.LogError(ude.Message);
                    errorCount++;
                    if (errorCount >= emailThreshold)
                    {
                        //var recipients = SetUpRecipients();
                        //await _emailService.SendUrgentEmail(recipients, "Undesirable Data Loss Notification",
                        //ExceptionHtmlContent(ude, stopwatch));
                        errorCount = 0;
                    }
                    await _etlService.FinishJobAsync(ordersJob, "Failure");
                }
                catch (Exception e)
                {
                    _logger.LogError(e.Message);
                    //var recipients = SetUpRecipients();
                    //await _emailService.SendUrgentEmail(recipients, "ETL Failure Notification",
                    //    ExceptionHtmlContent(e, stopwatch));
                    await _etlService.FinishJobAsync(ordersJob, "Failure");
                }
                finally
                {
                    _logger.LogInformation($"===== Entire process took: {stopwatch.Elapsed} =====");
                    _running = false;
                    _ordersJob = null;
                }
            }
        }

        private static string ExceptionHtmlContent(Exception e, Stopwatch stopwatch) =>
            $@"
<p><strong>Environment:</strong> {_environmentName ?? "UNKNOWN (Set 'ENV_NAME' in App Service > Slot > Configuration > Application Settings)"}</p>
<p><strong>Error Message:</strong> {e.Message}</p>
<p><strong>Time Elapsed:</strong> {stopwatch.Elapsed.ToHumanReadableString()}</p>
<p>
  <strong>StackTrace:</strong>
  <pre>{e}</pre>
</p>

";

        private IReadOnlyCollection<UserProfile> SetUpRecipients()
        {
            var recipients = _emails.Support.Select(emailContact => new UserProfile
            {
                Id = emailContact.Email,
                GivenName = emailContact.GivenName,
                Surname = emailContact.Surname
            }).ToList();
            return recipients;
        }
    }
}