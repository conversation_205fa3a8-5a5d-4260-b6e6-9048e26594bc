# Legacy pipeline - DEPRECATED
# This pipeline has been replaced by azure-pipelines-backend.yml and azure-pipelines-frontend.yml
# Please use the new pipelines for Azure-based deployments

# This file is kept for reference during the migration period
# TODO: Remove this file after successful migration to Azure

trigger: none # Disabled - use new pipelines

pool:
  vmImage: 'ubuntu-latest'

steps:
- script: echo "This pipeline has been deprecated. Please use azure-pipelines-backend.yml or azure-pipelines-frontend.yml"
  displayName: 'Migration Notice'
