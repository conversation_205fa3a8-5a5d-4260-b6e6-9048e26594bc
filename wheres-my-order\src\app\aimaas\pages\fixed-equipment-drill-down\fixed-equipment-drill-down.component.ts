import { Location } from '@angular/common';
import {
    AfterViewInit,
    Component,
    OnDestroy,
    OnInit,
    ViewChild
} from '@angular/core';
import { DxDataGridComponent } from 'devextreme-angular/ui/data-grid';
import DataSource from 'devextreme/data/data_source';
import { exportDataGrid } from 'devextreme/excel_exporter';
import dxDataGrid from 'devextreme/ui/data_grid';
import { Workbook } from 'exceljs';
import { saveAs } from 'file-saver';
import { combineLatest, Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { Breadcrumb } from '../../../shared/components';
import { DataGridService, UsersService } from '../../../shared/services';
import {
    AnomaliesRecommendations,
    Asset,
    AssetAttachment,
    AssetComponent,
    AssetInspection,
    AssetManagementSite,
    InspectionAttachment,
    RowExpandingHandler
} from '../../models';
import { SiteLabelPipe } from '../../pipes';
import { CredoSoftService } from '../../services';
@Component({
    selector: 'app-fixed-equipment-drill-down',
    templateUrl: './fixed-equipment-drill-down.component.html',
    styleUrls: ['./fixed-equipment-drill-down.component.scss']
})
export class FixedEquipmentDrillDownComponent
    implements OnInit, AfterViewInit, OnDestroy
{
    isLoading: boolean;
    selectedAssetId: any;
    storageKey: string = 'datagrid-state';
    popupVisible = false;
    components: any[];
    inspections: any[];
    assetDataSource: DataSource;
    assetComponentMap: AssetComponent[];
    generalAnalysis: any[];
    assetGeneralAnalysisMap: GeneralAnalysis[];
    assetInspectionMap: any;
    currentFilter: any = [];
    assetAttachments: AssetAttachment[] = [];
    inspectionAttachments: InspectionAttachment[] = [];
    currentAssetDetails: Asset;
    inspectionSchedule: any[];
    submissionsSchedule: any[];
    visibleAssetAttachments: AssetAttachment[];
    componentAttachments: AssetAttachment[] = [];
    submissions: any[];
    visibleInspectionAttachments: InspectionAttachment[] = [];
    anomaliesData: any;
    submissionPopupVisible: boolean = false;
    initialAnomaly: AnomaliesRecommendations = null;
    submissionPopupTitle: string = 'Anomaly Update';
    availableSites: AssetManagementSite[];
    selectedSite: AssetManagementSite;
    userId: string;
    breaCrumbLabel: string = 'Equipment List';
    isDataLoaded: boolean = false;
    currentAssetId: string;
    currentUser: UserProfile;
    allAssets: Asset[] = [];
    canShowEquipmentList: boolean = true;
    private routerSubscription: Subscription;
    equipmentPopupTitle: string = ' ';
    addUserPreferences = (
        useri: string,
        storageKey: string,
        values: string
    ) => {
        if (this.credoService) {
            this.credoService
                .addPreference(useri, storageKey, values)
                .subscribe(
                    (response) => {
                        console.log('Preference added successfully:', response);
                    },
                    (error) => {
                        //    console.error('Error adding preference:', error);
                    }
                );
        } else {
            console.error('CredoService is undefined!');
        }
    };
    saveState = (state) => {
        this._users.currentProfile$.subscribe((user: any) => {
            this.userId = user.id;
        });
        this.addUserPreferences(
            this.userId,
            'equipment',
            JSON.stringify(state)
        );
    };
    loadState = async () => {
        try {
            const response = await this.getUserPreference();
            if (response && response.equipment) {
                const equipmentValue = JSON.parse(response.equipment);
                equipmentValue.filterValue = this.currentFilter;
                return equipmentValue;
            } else {
                console.error('No equipment preference found in response.');
                return null;
            }
        } catch (error) {
            console.error('Error loading preference:', error);
            return null;
        }
    };
    @ViewChild(DxDataGridComponent)
    grid: DxDataGridComponent;

    constructor(
        private readonly credoService: CredoSoftService,
        private readonly _sitePipe: SiteLabelPipe,
        private readonly _users: UsersService,
        private readonly _grid: DataGridService,
        private route: ActivatedRoute,
        private router: Router,
        private location: Location
    ) {}

    crumbs: Breadcrumb[];

    ngAfterViewInit(): void {
        //  this.grid?.instance?.beginCustomLoading('');
    }
    getUserPreference(): Promise<any> {
        this._users.currentProfile$.subscribe((user: any) => {
            this.userId = user.id;
        });
        return new Promise((resolve, reject) => {
            this.credoService.getPreference(this.userId, 'equipment').subscribe(
                (response) => {
                    if (response && response.equipment) {
                        resolve(response);
                    } else {
                        reject('No equipment preference found in response.');
                    }
                },
                (error) => {
                    reject('Error fetching preference: ' + error);
                }
            );
        });
    }
    addUserPreference(useri: string, storageKey: string, values: string): void {
        if (this.credoService) {
            this.credoService
                .addPreference(useri, storageKey, values)
                .subscribe(
                    (response) => {
                        // console.log('Preference added successfully:', response);
                    },
                    (error) => {
                        // console.error('Error adding preference:', error);
                    }
                );
        } else {
            console.error('CredoService is undefined!');
        }
    }
    ngOnInit(): void {
        //this.isLoading = true;
        combineLatest([
            this.credoService.getAllAssetsAsDataSource(),
            this.credoService.assets$,
            this.credoService.inspections$,
            this.credoService.assetManagementSites$,
            this._users.currentProfile$,
            this.credoService.anomalies$
            // this.credoService.generalAnalysis$
        ])
            // finalize(() => this.grid?.instance?.endCustomLoading())
            .pipe()
            .subscribe(
                async ([
                    ds,
                    assets,
                    inspections,
                    sites,
                    currentUser,
                    anomalies
                ]: [
                    DataSource,

                    Asset[],
                    AssetInspection[],
                    AssetManagementSite[],
                    UserProfile,
                    AnomaliesRecommendations[]
                    //GeneralAnalysis[]
                ]) => {
                    this.assetDataSource = ds;
                    this.inspections = inspections;
                    this.allAssets = assets;
                    this.currentUser = currentUser;
                    if (history.state?.data?.currentFilter)
                        this.currentFilter = history.state.data.currentFilter;
                    this.availableSites = sites;
                    this.userId = currentUser.email;
                    const roles = currentUser.roles.map((role) =>
                        role.toLowerCase()
                    );
                    if (roles) {
                        if (roles.includes('aimaas:demo')) {
                            this.availableSites = this.availableSites.filter(
                                (site) =>
                                    site.locationid ==
                                    Number('635140707384299520')
                            );
                        } else if (
                            !roles.includes('app:admin') &&
                            !roles.includes('aimaas:admin') &&
                            currentUser.assetManagementSiteIds
                        ) {
                            this.availableSites = this.availableSites.filter(
                                (site) =>
                                    currentUser.assetManagementSiteIds.includes(
                                        site.locationid
                                    )
                            );
                        }
                    }
                    this.selectedSite =
                        this.availableSites.find(
                            (site) =>
                                site.locationid ==
                                Number(localStorage.getItem('selectedSite'))
                        ) ?? this.availableSites[0];

                    if (history?.state?.data?.assetObjIds) {
                        if (history.state.data.assetObjIds.length <= 0) {
                            this.currentFilter = [
                                'assetid',
                                'noneof',
                                (
                                    await this.assetDataSource.store().load()
                                )?.map((item) => item.assetid)
                            ];
                        } else {
                            this.currentFilter = [
                                'assetid',
                                'anyof',
                                history.state.data.assetObjIds
                            ];
                        }
                    }
                    this.isLoading = false;
                    this.isDataLoaded = true;
                    this.route.paramMap.subscribe(() => {
                        this.handleQueryParams();
                    });
                    this.routerSubscription = this.router.events
                        .pipe(
                            filter((event) => event instanceof NavigationEnd),
                            filter(() =>
                                this.router.url.includes('aimaas/drilldown')
                            )
                        )
                        .subscribe(() => {
                            this.handleQueryParams();
                        });
                }
            );
        this.updateBreadcrumbs();
    }
    ngOnDestroy(): void {
        if (this.routerSubscription) {
            this.routerSubscription.unsubscribe();
        }
    }
    handleQueryParams() {
        this.route.queryParams.subscribe((params) => {
            const assetid = params['assetid'];

            // If no asset ID is present, show the equipment list and return
            if (!assetid) {
                this.canShowEquipmentList = true;
                return;
            }

            // Get asset details for the specified asset ID
            const assetDetails = this.allAssets?.find(
                (asset) => asset.id == assetid
            );

            // If no matching asset is found, do not show the equipment list
            if (!assetDetails) {
                this.canShowEquipmentList = false;
                return;
            }

            // Check if the asset's location matches the selected site's location
            if (
                String(this.selectedSite.locationid) !== assetDetails.locationid
            ) {
                // Find the corresponding site for the asset's location
                const filteredAssetSite = this.availableSites.find(
                    (site) => site.locationid == Number(assetDetails.locationid)
                );

                // If a matching site is found, change the site and update the asset
                if (filteredAssetSite) {
                    this.changeSite({
                        selectedItem: filteredAssetSite
                    });
                    this.assetSelectionChanged({
                        data: assetDetails
                    });
                    this.canShowEquipmentList = true;
                } else {
                    this.canShowEquipmentList = false;
                }
            } else {
                // If the asset's location matches the selected site, update the asset directly
                this.assetSelectionChanged({ data: assetDetails });
                this.canShowEquipmentList = true;
            }
        });
    }
    restoreAssetsDefaultsClicked = async (e) => {
        const result = await this._grid.resetGridState(this.grid);
        if (result) {
            this.addUserPreferences(
                this.userId,
                'equipment',
                JSON.stringify('')
            );
        }
    };

    clientSubmissionTitleValueChange(e: any) {
        this.submissionPopupTitle = e;
    }
    clientDataFormSubmitted(e: any) {
        this.submissionPopupVisible = false;
    }
    clientSubmitDataOnclick(e: string) {
        if (e === 'frombuttonclick') {
            this.initialAnomaly = null;
        }
        this.submissionPopupVisible = !this.submissionPopupVisible;
    }
    updateBreadcrumbs() {
        if (history.state?.breadCrumbLabel) {
            this.breaCrumbLabel = history.state.breadCrumbLabel
                ? history.state.breadCrumbLabel
                : 'Equipment';
        }
        this.crumbs = [
            { label: 'KPI Dashboards', route: '/aimaas/dashboards' },
            { label: this.breaCrumbLabel, route: '/aimaas/drilldown' }
        ];
    }
    onContentReady(event) {
        this.grid?.instance?.endCustomLoading();
    }
    onCellPrepared(event) {
        if (event.rowType == 'data' || this.isDataLoaded) {
            this.isLoading = false;
        } else {
            this.isLoading = true;
        }
    }
    getComponentsForAsset(id: string): AssetComponent[] {
        if (!this.components) {
            return [];
        }
        return this.components.filter((item) => item.assetid === id);
    }
    getGeneralAnalysisForAsset(id: string): GeneralAnalysis[] {
        if (!this.generalAnalysis) {
            return [];
        }
        return this.generalAnalysis.filter((item) => item.assetid === id);
    }
    getInspectionsForAsset(id: string): AssetInspection[] {
        if (!this.inspections) {
            return [];
        }

        return this.inspections?.filter((item) => item.assetid === id);
    }
    assetSelectionChanged(e) {
        const asset = e.data;
        this.equipmentPopupTitle = `EQUIPMENT - ${asset.assetid}`;
        this.location.replaceState(`/aimaas/drilldown?assetid=${asset.id}`);
        this.assetGeneralAnalysisMap = [];
        this.assetComponentMap = [];
        var comp = this.assetComponentMap;
        this.currentAssetId = asset.id;
        this.credoService.getAllComponents(asset.id).subscribe((data) => {
            this.assetComponentMap = data;
            comp = this.assetComponentMap.sort((a, b) => {
                const clientCompare = a.componentname.localeCompare(
                    b.componentname
                );
                if (clientCompare !== 0) {
                    return clientCompare;
                }
            });
        });

        this.visibleAssetAttachments = this.assetAttachments.filter(
            (attachment) => attachment.assetid === asset.id
        );

        this.currentAssetDetails = asset;
        this.inspectionSchedule = this.getInspectionsForAsset(asset.id);
        this.popupVisible = true;
    }

    inspectionSelectionChanged(e) {
        const inspection = e.selectedRowsData[0];
        this.visibleInspectionAttachments = this.inspectionAttachments.filter(
            (attachment) => attachment.inspectionid === inspection.inspectionid
        );
        this.visibleAssetAttachments = this.assetAttachments.filter(
            (attachment) => attachment.CLIENTID === inspection.objid
        );
    }
    formatPID(data) {
        if (data.pid == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(data.pid, 'text/html');
        return doc.documentElement.textContent ?? ' ';
    }
    formatLocalJudictional(data) {
        if (data.localjuridictional == null) {
            return ' ';
        }
        const doc = new DOMParser().parseFromString(
            data.localjuridictional,
            'text/html'
        );
        return doc.documentElement.textContent ?? ' ';
    }
    onRowExpanding(e: {
        cancel: boolean;
        component: dxDataGrid;
        element: HTMLElement;
        key: any;
        model: any;
    }) {
        RowExpandingHandler.handle(e);
    }

    customDisplayExpr = (site: any): string => {
        if (site) return this._sitePipe.transform(site);
    };

    changeSite(selectedSite) {
        let _selectedSite = selectedSite.selectedItem;
        if (_selectedSite == null) {
            setTimeout(() => {
                this.selectedSite = this.availableSites[0];
                localStorage.setItem(
                    'selectedSite',
                    String(this.selectedSite.locationid)
                );
            }, 1);
        } else {
            this.selectedSite = selectedSite.selectedItem;
            localStorage.setItem(
                'selectedSite',
                String(this.selectedSite.locationid)
            );
        }
        this.assetDataSource.filter(
            (asset) => asset.locationid === this.selectedSite.locationid
        );
        this.assetDataSource.load();
    }
    async onExporting(event) {
        const workbook = new Workbook();
        const worksheet = workbook.addWorksheet('Assets');
        await exportDataGrid({
            component: event.component,
            worksheet
        });
        const buffer: BlobPart = await workbook.xlsx.writeBuffer();
        saveAs(
            new Blob([buffer], { type: 'application/octet-stream' }),
            'Assets.xlsx'
        );
    }
    closePopup() {
        this.popupVisible = false;
        this.equipmentPopupTitle = ' ';
        this.location.replaceState('/aimaas/drilldown');
    }
}

import {
    HttpEvent,
    HttpHandler,
    HttpInterceptor,
    HttpRequest
} from '@angular/common/http';
import { Inject, Injectable, InjectionToken } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { timeout } from 'rxjs/operators';
import { UserProfile } from '../../../profile/models/user-profile';
import { GeneralAnalysis } from '../../models/general-analysis';

export const DEFAULT_TIMEOUT = new InjectionToken<number>('defaultTimeout');

@Injectable()
export class TimeoutInterceptor implements HttpInterceptor {
    constructor(@Inject(DEFAULT_TIMEOUT) protected defaultTimeout: number) {}

    intercept(
        req: HttpRequest<any>,
        next: HttpHandler
    ): Observable<HttpEvent<any>> {
        const timeoutValue = req.headers.get('timeout') || this.defaultTimeout;
        const timeoutValueNumeric = Number(timeoutValue);

        return next.handle(req).pipe(timeout(timeoutValueNumeric));
    }
}
