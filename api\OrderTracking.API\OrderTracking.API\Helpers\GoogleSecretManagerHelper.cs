﻿using Google.Cloud.SecretManager.V1;

public class GcpSecretManagerHelper
{
    public static string GetSecret(string secretId, string projectId)
    {
        // Create the client.
        SecretManagerServiceClient client = SecretManagerServiceClient.Create();

        // Build the resource name.
        SecretVersionName secretVersionName = new SecretVersionName(projectId, secretId, "1");

        // Access the secret version.
        AccessSecretVersionResponse result = client.AccessSecretVersion(secretVersionName);

        // Return the secret payload.
        return result.Payload.Data.ToStringUtf8();
    }

    public static string GetSecret(string secretId, string projectId, string version)
    {
        // Create the client.
        SecretManagerServiceClient client = SecretManagerServiceClient.Create();

        string secretVersion = string.IsNullOrEmpty(version) ? version : "1";

        // Build the resource name.
        SecretVersionName secretVersionName = new SecretVersionName(projectId, secretId, version);

        // Access the secret version.
        AccessSecretVersionResponse result = client.AccessSecretVersion(secretVersionName);

        // Return the secret payload.
        return result.Payload.Data.ToStringUtf8();
    }

}


