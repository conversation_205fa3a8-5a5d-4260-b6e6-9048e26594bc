import moment from 'moment';
import { AssetCategoryAPICode } from '..';
import { isNullOrUndefined } from '../../../shared/helpers';
import {
    InspectionStatusBreakdown,
    ProjectVm,
    StatusesByBin,
    TaskVM,
    WorkTypePercentages
} from '../view-models';
import {
    CosmosAsset,
    CosmosAttribute,
    CosmosProject,
    CosmosTask,
    CosmosWorkOrder,
    ListChangeLog,
    ProjectActivityItem,
    // Legacy Firebase types for backward compatibility
    FirebaseAsset,
    FirebaseAttribute,
    FirebaseProject,
    FirebaseTask,
    FirebaseWorkOrder
} from './models';

export function getCurrentEntries(listChangeLog: ListChangeLog) {
    const sorted = Object.values(listChangeLog).sort((a, b) => a.T - b.T);
    let allEntries = Array.from(new Set(sorted.map((x) => x.V)));
    const allCopy = [...allEntries];
    for (const entry of allCopy) {
        const lastMatch = sorted
            .filter((x) => x.V === entry)
            .reduce(
                (prev, curr) =>
                    prev === null || curr.T >= prev.T ? curr : prev,
                null
            );
        if (lastMatch.A === 'Removed')
            allEntries = allEntries.filter((x) => x !== entry);
    }

    allEntries = allEntries.filter((x) => !isNullOrUndefined(x));

    return allEntries;
}

export function getCurrentValue<T>(attribute: CosmosAttribute<T>): T {
    if (!attribute || !attribute.ValueChangeLog) return attribute?.Value;

    const sorted = Object.values(attribute.ValueChangeLog).sort(
        (a, b) => a.T - b.T
    );
    const latest = sorted[sorted.length - 1];
    return latest ? latest.V : attribute.Value;
}

// Legacy Firebase helper functions (for backward compatibility)
export function getCurrentValueFirebase<T>(attribute: FirebaseAttribute<T>): T {
    return getCurrentValue(attribute as CosmosAttribute<T>);
}

// Additional helper functions for Azure Cosmos DB migration
export function convertFirebaseToCosmosAttribute<T>(firebaseAttr: FirebaseAttribute<T>): CosmosAttribute<T> {
    return firebaseAttr as CosmosAttribute<T>;
}

export function convertCosmosToFirebaseAttribute<T>(cosmosAttr: CosmosAttribute<T>): FirebaseAttribute<T> {
    return cosmosAttr as FirebaseAttribute<T>;
}
