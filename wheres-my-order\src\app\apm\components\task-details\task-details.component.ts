import { Component, inject } from '@angular/core';
import { Firestore } from '@angular/fire/firestore';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { UsersService } from '../../../shared/services/users.service';
import { Asset, DataInterface, Task, WorkOrder } from '../../models/data/data-interface';

@Component({
  selector: 'app-task-details',
  templateUrl: './task-details.component.html',
  styleUrls: ['./task-details.component.scss']
})

export class TaskDetailsComponent {

  // Variables
  
  taskId: string
  currentUser$ = this._users.currentProfile$

  // Views
  
  firestore = inject(Firestore)

  // New

  workOrder: WorkOrder
  task: Task
  asset: Asset

  constructor(
    private readonly _activatedRoute: ActivatedRoute, 
    private readonly _toasts: ToastrService,
    private readonly _users: UsersService
    ) { 

      DataInterface.setFirestore(this.firestore)

    this._activatedRoute.paramMap.subscribe((params)=>{
      this.taskId = this._activatedRoute.snapshot.queryParams?.task_id
      this.loadWorkOrder(params.get("id"), this.taskId)
     });

  }

  loadWorkOrder(workOrderId, taskIdd) {
    DataInterface.getWorkOrder(workOrderId).subscribe(w => {
      this.workOrder = new WorkOrder(w)
      DataInterface.getAsset(this.workOrder.assetId).subscribe(a => {
        this.asset = new Asset(a)
        DataInterface.getTask(taskIdd).subscribe(t => {
          this.task = new Task(t)
        })
      })
    })
  }

  onSaveClicked(e) {

    DataInterface.getWorkOrder(this.task.workOrderId).subscribe(w=> {

      var old = new WorkOrder(w)
      if (this.workOrder.jobScope != old.jobScope) {
        DataInterface.setWorkOrderField(
          this.task.workOrderId, 
          WorkOrder.Field.JOB_SCOPE, 
          this.workOrder.jobScope).subscribe( _ => this.showSuccess())
      }

      if (this.workOrder.inspectionSummary != old.inspectionSummary) {
        DataInterface.setWorkOrderField(
          this.task.workOrderId, 
          WorkOrder.Field.INSPECTION_SUMMARY, 
          this.workOrder.inspectionSummary).subscribe( _ => this.showSuccess())
      }

      if (this.workOrder.applicableDamageMechanisms != old.applicableDamageMechanisms) {
        DataInterface.setWorkOrderField(
          this.task.workOrderId, 
          WorkOrder.Field.APPLICABLE_DAMAGE_MECHANISMS, 
          this.workOrder.applicableDamageMechanisms).subscribe( _ => this.showSuccess())
      }

      if (this.workOrder.relevantIndications != old.relevantIndications) {
        DataInterface.setWorkOrderField(
          this.task.workOrderId, 
          WorkOrder.Field.RELEVANT_INDICATIONS, 
          this.workOrder.relevantIndications).subscribe( _ => this.showSuccess())
      }

      if (this.workOrder.recommendations != old.recommendations) {
        DataInterface.setWorkOrderField(
          this.task.workOrderId, 
          WorkOrder.Field.RECOMMENDATIONS, 
          this.workOrder.recommendations).subscribe( _ => this.showSuccess())
      }

    })
    
  }

  showSuccess(){
    if(this._toasts.currentlyActive == 1) {
      this._toasts.success("Data was updated correctly", "Success")
    }
  }


}