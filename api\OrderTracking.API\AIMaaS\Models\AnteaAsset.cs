﻿using Google.Cloud.Firestore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AIMaaS.Models
{
    public class ChamberData
    {
        public string NAME { get; set; }
        public string DESIGNPRESSURE { get; set; }
        public string FULLVACCUM { get; set; }
        public string MINTEMP { get; set; }
        public string DESIGNTEMP { get; set; }
        public string FLUID { get; set; }
    }
    public class AnteaAsset
    {
        public string ID { get; set; }
        public string LINK { get; set; }
        public string IMAGE { get; set; }
        public string SCLASS { get; set; }
        public string RiskClass { get; set; }
        public string ASSETID { get; set; }
        public string AREAID { get; set; }
        public string AREANAME { get; set; }
        public string DESCRIPTION { get; set; }
        public string SERVICE { get; set; }
        public string EQUIPCATEGORY { get; set; }
        public string ASSETTYPE { get; set; }
        public string ASSETCATEGORY { get; set; }
        public string ORIENTATION { get; set; }
        public string CONSTRUCTIONCODE { get; set; }
        public string INSPECTIONCODE { get; set; }

        public string MANUFACTURER { get; set; }
        public int CONSTRUCTIONYEAR { get; set; }
        public string DIMENSIONS { get; set; }
        public string SERIALNUMBER { get; set; }
        public string NATIONALBOARD { get; set; }
        public string LOCALJURIDICTIONAL { get; set; }
        public string PID { get; set; }
        public string CLIENTID { get; set; }
        public string CLIENTNAME { get; set; }
        public string LOCATIONID { get; set; }
        public string LOCATIONNAME { get; set; }
        public string CHAMBERS { get; set; }

    }
    public class AssetManagementSites
    {
        public string CLIENTID { get; set; }
        public string CLIENTNAME { get; set; }
        public string LOCATIONNAME { get; set; }
        public string LOCATIONID { get; set; }
        
    }

    public class EquipmentData
    {
        public string ClientID { get; set; }
        public string ClientName { get; set; }
        public string LocationID { get; set; }
        public string LocationName { get; set; }
        public List<EquipementIdStrategies> EquipmentList { get; set; }
    }
    public class EquipementIdStrategies
    {
        public string ID { get; set; }
        public string STRATEGYCLASSNAME { get; set; }
    }

    public class AssetAttachments
    {
        public string CLIENTID { get; set; }
        public string CLIENTNAME { get; set; }
        public string LOCATIONNAME { get; set; }
        public string LOCATIONID { get; set; }

        public string ASSETID { get; set; }

        public string DOCUMENTID { get; set; }
        public string FILENAME { get; set; }
        public string FILETYPE { get; set; }
        public string DOCUMENTTYPE { get; set; }
        public string CREATEDDATE { get; set; }
        public string FILELINK { get; set; }
        public string DESCRIPTION { get; set; }

    }
    public class AssetComponents
    {
        public string CLIENTID { get; set; }
        public string CLIENTNAME { get; set; }
        public string LOCATIONNAME { get; set; }
        public string LOCATIONID { get; set; }
        public string ASSETID { get; set; }
        public string COMPONENTID { get; set; }
        public string COMPONENTNAME { get; set; }
        public string DESCRIPTION { get; set; }
        public string MATERIAL { get; set; }
        public string OUTSIDEDIAMETER { get; set; }
        public string HEADTYPE { get; set; }
        public string NOTES { get; set; }

        public string JOINTEFFICIENCY { get; set; }
        public string ALLOWABLESTRESS { get; set; }
        public string NOMINALTHICKNESS { get; set; }
        public string CORROSIONALLOWANCE { get; set; }
        public string REASSESEDLIMITTHICKNESS { get; set; }
 
    }
    public class CorrosionAnalysis
    {
        public string NAME { get; set; }
        public string POSITION { get; set; }
        public string NOMINALTHICKNESS { get; set; }
        public string RETIREMENTTHICKNESS { get; set; }
        public string READINGDATE { get; set; }
        public string READING { get;set; }
        //public string CURRENTLIMIT { get; set; }
        //public string MINIMUMTHICK { get; set; }
        //public string STCR { get; set; }
        //public string LTCR { get; set; }
        //public string STRL { get; set; }
        //public string LTRL { get; set; }
    }
    public class GeneralAnalysis
    {
        public string CLIENTID { get; set; }
        public string CLIENTNAME { get; set; }
        public string LOCATIONNAME { get; set; }
        public string LOCATIONID { get; set; }
        public string ASSETID { get; set; }
        public string COMPONENTID { get; set; }
        public string RETIREMENTDATE { get; set; }
        public string REMAININGLIFE { get; set; }
        public string DRIVINGCOMPONENT {  get; set; }
        public string DRIVINGCML { get; set; }
        public string LTCORROSIONRATE { get; set; }
        public string STCORROSIONRATE { get; set; }
        public string LASTDATE { get; set; }
        public string LASTVALUE { get; set; }
        public string PREVIOUSDATE { get; set; }
        public string PREVIOUSVALUE { get; set; }
        public string FIRSTDATE { get; set; }
        public string FIRSTVALUE { get; set; }
        public string NOMINALTHICKNESS { get; set; }
        public string RETIREMENTTHICKNESS { get; set; }
    }
    public class Inspections
    {
        public string CLIENTID { get; set; }
        public string CLIENTNAME { get; set; }
        public string LOCATIONNAME { get; set; }
        public string LOCATIONID { get; set; }
        public string ASSETID { get; set; }
        public string ASSETDESCRIPTION { get; set; }
        public string ASSETIDNAME { get; set; }
        public string LINENUMBER { get; set; }
        public string OPERATIONORSCHEDULE { get; set; }
        public string INSPECTIONASSETCATEGORY { get; set; }
        public string INSPECTIONID { get; set; }
        public string AREA {  get; set; }
        public string RISKCLASS { get; set; }
        public string INSPECTIONTYPE { get; set; }
        public string COMPLETEDINSPECTIONDUE { get; set; }
        public string INSPECTIONDATE { get; set; }
        public string RESULT { get; set; }
        public string INSPECTIONSTATE { get; set; }
        public string NEXTINSPECTIONDUE { get; set; }
        public string SCHEDULETYPE { get; set; }
        public string FREQUENCY { get; set; }
        public string SCHEDULESTATUS { get; set; }
        public string INSPECTIONSTATUS { get; set; }
        public string NOTES { get; set; }
        public string PERFORMER { get; set; }
        public string ASSETMANAGEMENTCATEGORY { get; set; }
        public string SCHEDULEID { get; set; }
        public string PLANOPERATIONID { get; set; }
        public string LASTDATE { get; set; }
        public string NEXTDATE { get; set; }
        public string INSPECTIONDUE { get; set; }
        public string DATE { get; set; }
        public string OPERATIONTYPE { get; set; }
        //public string LAW { get; set; }
        public string ASSETSTATUS { get; set; }
        public string NEXTDUEDATENOTES {  get; set; }
    }

    public class InspectionAttachments
    {
        public string CLIENTID { get; set; }
        public string CLIENTNAME { get; set; }
        public string LOCATIONNAME { get; set; }
        public string LOCATIONID { get; set; }

        public string ASSETID { get; set; }

        public string INSPECTIONID { get; set; }
        public string DOCUMENTID { get; set; }
        public string FILENAME { get; set; }
        public string DOCUMENTTYPE { get; set; }
        public string DESCRIPTION { get; set; }
        public string CREATIONDATE { get; set; }
        public string FILELINK { get; set; }
        public string FILETYPE { get; set; }
    }
    public class Anomalies
    {
        public string CLIENTID { get; set; }
        public string CLIENTNAME { get; set; }
        public string LOCATIONNAME { get; set; }
        public string ASSETID { get; set; }
        public string ASSETNAME { get; set; }
        public string LOCATIONID { get; set; }
        public string OPERATIONID { get; set; }
        public string ANOMALYID { get; set; }
        public string ANOMALYPRIORITY { get; set; }
        public string ANOMALYTYPE { get; set; }
        public string ANOMALYDESCRIPTION { get; set; }
        public string RESOLUTIONSTATE { get; set; }
        public string ANOMALY { get; set; }
        public string INSPECTIONOPERATIONINSTANCE { get; set; }
        public string DETECTIONDATE { get; set; }
        public string OPERATIONDATE { get; set; }
        public string PROPOSEDRECOMMEMENDATION { get; set; }

    }

    public class ResponseSubmissions
    {
        public string Id { get; set; }
        public string CreatedDate { get; set; }
        public string SubmissionType { get; set; }
        public string AnomalyId { get; set; }
        public string ClientClosedDate { get; set; }
        public string Comment { get; set; }
        public string AssetId { get; set; }
        public string ServiceType { get; set; }
        public string SubmittedUser { get; set; }

    }
    public class Submissions
    {
        public string Id { get; set; }
        public string ClientFacility { get; set; }
        public string CostCenter { get; set; }
        public string CreatedDate { get; set; }
        public string SubmissionType { get; set; }
        public SubmissionAnomalyInfo AnomalyInfo { get; set; }
        public string ClientClosedDate { get; set; }
        public string Comment { get; set; }
        public List<SubmissionAssetInfo> AssetInfo { get; set; }
        public string ServiceType { get; set; }
        // public string SubmittedUser { get; set; }
        public List<SubmissionFileUpload> Documents { get; set; }
        public SubmissionSenderInfo SenderInfo { get; set; }
        public List<SubmissionInspectionInfo> InspectionInfo { get; set; }
        public string LocationId { get; set; }

    }
    public class SubmissionAnomalyInfo
    {
        //public string AnomalyId { get; set; }
        public string AnomalyNumber { get; set; }
        public string AnomalyPriority { get; set; }
        public string AnomalyType { get; set; }
        public string AnomalyInspectionOperation { get; set; }
    }
    public class SubmissionFileUpload
    {
        public string FileName { get; set; }
        public string FileType { get; set; }
        public string FileData { get; set; }
    }
    public class SubmissionAssetInfo
    {
        public string AssetId { get; set; }
        public string AssetName { get; set; }
        public string AssetDescription { get; set; }
    }
    public class SubmissionSenderInfo
    {
        public string EmailId { get; set; }
        public string GivenName { get; set; }
        public string Surname { get; set; }
    }
    public class SubmissionInspectionInfo
    {
        public string InspectionId { get; set; }
        public string InspectionType { get; set; }
    }
    public class SubmissionsJsonFile
    {
        public string Id { get; set; }
        public string ClientFacility { get; set; }
        public string CostCenter { get; set; }
        public string CreatedDate { get; set; }
        public string SubmissionType { get; set; }
        public SubmissionAnomalyInfo AnomalyInfo { get; set; }
        public string ClientClosedDate { get; set; }
        public string Comment { get; set; }
        public List<SubmissionAssetInfo> AssetInfo { get; set; }
        public string ServiceType { get; set; }
        public List<SubmissionJsonDocumentData> Documents { get; set; }
        public SubmissionSenderInfo SenderInfo { get; set; }

    }
    public class SubmissionJsonDocumentData
    {
        public string FileName { get; set; }
        public string FileLink { get; set; }

    }

    [FirestoreData]
    public class FireStoreSubmissions
    {
        [FirestoreProperty]
        public string Id { get; set; }
        [FirestoreProperty]
        public string ClientFacility { get; set; }
        [FirestoreProperty]
        public string CostCenter { get; set; }

        [FirestoreProperty]
        public string CreatedDate { get; set; }

        [FirestoreProperty]
        public string SubmissionType { get; set; }

        [FirestoreProperty]
        public FireStoreSubmissionAnomalyInfo AnomalyInfo { get; set; }

        [FirestoreProperty]
        public string ClientClosedDate { get; set; }

        [FirestoreProperty]
        public string Comment { get; set; }

        [FirestoreProperty]
        public List<FireStoreSubmissionAssetInfo> AssetInfo { get; set; }

        [FirestoreProperty]
        public string ServiceType { get; set; }

        [FirestoreProperty]
        public string SubmittedUser { get; set; }

        [FirestoreProperty]
        public List<FireStoreSubmissionFileUpload> Documents { get; set; }
        [FirestoreProperty]
        public List<FireStoreSubmissionInspectionInfo> InspectionInfo { get; set; }
    }
    [FirestoreData]
    public class FireStoreSubmissionAnomalyInfo
    {
        [FirestoreProperty]
        public string AnomalyId { get; set; }
        [FirestoreProperty]
        public string AnomalyNumber { get; set; }
        [FirestoreProperty]
        public string AnomalyPriority { get; set; }
        [FirestoreProperty]
        public string AnomalyType { get; set; }
        [FirestoreProperty]
        public string AnomalyInspectionOperation { get; set; }
    }
    [FirestoreData]
    public class FireStoreSubmissionFileUpload
    {
        [FirestoreProperty]
        public string FileName { get; set; }

        [FirestoreProperty]
        public string FileType { get; set; }
    }
    [FirestoreData]
    public class FireStoreSubmissionAssetInfo
    {
        [FirestoreProperty]
        public string AssetId { get; set; }
        [FirestoreProperty]
        public string AssetName { get; set; }
        [FirestoreProperty]
        public string AssetDescription { get; set; }
    }
    [FirestoreData]
    public class FireStoreSubmissionInspectionInfo
    {
        [FirestoreProperty]
        public string InspectionId { get; set; }
        [FirestoreProperty]
        public string InspectionType { get; set; }
    }
    public class SystemManagementCategories
    {
        public string SYSTEMCATEGORY { get; set; }
        public string CODE { get; set; }
    }


}
