﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using OrderTracking.API.Repositories;

namespace OrderTracking.API.Services
{
    public class RolesService : RolesRepository, IRolesService
    {
        #region Constructors

        public RolesService(IContainerFactory containerFactory, IServiceProvider serviceProvider, IConfiguration configuration) : base(containerFactory,
            configuration)
        {
            _authHistory = (IAuthHistoryService)serviceProvider.GetService(typeof(IAuthHistoryService));
            _httpContextAccessor = (IHttpContextAccessor)serviceProvider.GetService(typeof(IHttpContextAccessor));

            _userProfileRepository = new UserProfileRepository(containerFactory, configuration);
        }

        #endregion

        #region Fields and Constants

        private readonly IUserProfileRepository _userProfileRepository;
        private readonly IAuthHistoryService _authHistory;
        private readonly IHttpContextAccessor _httpContextAccessor;

        #endregion

        #region Interface Implementation

        public new async Task<Role> AddAsync(Role role)
        {
            role = VerifyProperties(role);
            var newRole = await base.AddAsync(role);
            if (newRole != null) await CreateChangeEventAsync(null, newRole);

            return newRole;
        }

        public async Task<IEnumerable<string>> GetGroupsAsync()
        {
            var roles = await GetAllAsync();
            return roles.Select(r => r.Group).Distinct();
        }

        public new async Task RemoveAsync(string id)
        {
            var role = await base.GetAsync(id);
            if (role == null) return;

            await base.RemoveAsync(id);
            var usersToUpdate = await _userProfileRepository.GetUsersForRoleAsync(id);
            foreach (var user in usersToUpdate)
            {
                user.Roles.Remove(id);
                await _userProfileRepository.UpdateAsync(user);
            }

            await CreateChangeEventAsync(role, null);
        }

        public override async Task<Role> UpdateAsync(Role role, string originalId)
        {
            var originalRole = await GetAsync(role.Id);
            role = VerifyProperties(role);
            if (originalId == null || originalId == role.Id)
            {
                //Simple update, no need to worry about replacing Id
                var updatedRole = await base.UpdateAsync(role);

                await CreateChangeEventAsync(originalRole, updatedRole);
                return updatedRole;
            }

            //More complex -- need to do both a swap and update the UserProfile.Roles[]
            var usersToUpdate = await _userProfileRepository.GetUsersForRoleAsync(originalId);
            var newRole = await UpsertAsync(role, originalId);

            await CreateChangeEventAsync(role, newRole);

            if (newRole != null)
            {
                var newRoleId = newRole.Id;
                foreach (var user in usersToUpdate)
                {
                    user.Roles.Remove(originalId);
                    user.Roles.Add(newRoleId);
                    await _userProfileRepository.UpdateAsync(user);
                }

                return newRole;
            }

            return null;
        }

        #endregion

        #region Private and Other Methods

        private async Task<UserProfile> GetCurrentUserAsync() =>
            await _userProfileRepository.GetAsync(_httpContextAccessor
                .HttpContext.User.Identity.Name
                .ToLower());

        private static Role VerifyProperties(Role role)
        {
            var r = new Role(role.Id);
            if (role.Group != r.Group || role.RoleName != r.RoleName)
            {
                // Role was created as a simple group:key string, or the key is changing.
                // Create new object which parses pieces and constitutes a full object
                role.Group = r.Group;
                role.RoleName = r.RoleName;
            }

            return role;
        }

        private async Task CreateChangeEventAsync(Role oldRole, Role newRole)
        {
            if (oldRole == null && newRole == null)
                throw new InvalidOperationException($"{nameof(oldRole)} & {nameof(newRole)} cannot both be null.");
            if (_httpContextAccessor.HttpContext.User.Identity.Name != null)
                await _authHistory.AddItemAsync(new ChangeEvent
                {
                    Id = Guid.NewGuid().ToString(),
                    Old = oldRole,
                    New = newRole,
                    CreatedAt = DateTime.UtcNow,
                    User = await GetCurrentUserAsync()
                });
        }

        #endregion
    }
}