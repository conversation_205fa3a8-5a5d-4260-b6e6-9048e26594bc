using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Google.Cloud.Firestore;
using Microsoft.Extensions.Options;
using OrderTracking.API.Extensions;
using OrderTracking.API.Interfaces;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Service class providing access to notifications
    /// </summary>
    public class NotificationsService : INotificationsService
    {
        private readonly CollectionReference _collection;

        #region Constructors

        /// <summary>
        ///     Constructs a NotificationsService class, injecting connection configuration
        /// </summary>
        /// <param name="options"></param>
        public NotificationsService(IFirestoreClientAdapter adapter, IOptions<Connections> options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            _collection = adapter.GetCollection(options.Value.Notifications);
        }

        #endregion

        #region Interface Implementation

        /// <summary>
        ///     Get notifications, dependent on the userId
        /// </summary>
        /// <param name="queryString"></param>
        /// <returns></returns>
        public async Task<IEnumerable<Notification>> GetItemByUserAsync(string userId)
        {
            var querySnapshot = await _collection.WhereEqualTo("recipient", userId).GetSnapshotAsync();
            return querySnapshot.Documents.Select(doc => doc.ConvertTo<Notification>());
        }

        /// <summary>
        ///     Update a notification
        /// </summary>
        /// <param name="id"></param>
        /// <param name="notification"></param>
        /// <returns></returns>
        public async Task UpdateItemAsync(string id, Notification notification)
        {
            if (notification == null) throw new ArgumentNullException(nameof(notification));

            await _collection.Document(id).SetAsync(notification);
        }

        #endregion
    }
}