// The file contents for the current environment will overwrite these during build.
// The build system defaults to the dev environment which uses `environment.ts`, but if you do
// `ng build --env=prod` then `environment.prod.ts` will be used instead.
// The list of which env maps to which file can be found in `.angular-cli.json`.

export const environment = {
    azureAd: {
        clientId: '***AZURE_AD_CLIENT_ID***',
        authority: 'https://login.microsoftonline.com/***TENANT_ID***',
        redirectUri: 'http://localhost:4200',
        postLogoutRedirectUri: 'http://localhost:4200'
    },
    azureStorage: {
        accountName: 'stakrakendev001',
        containerName: 'app-data',
        sasToken: '***AZURE_STORAGE_SAS_TOKEN***'
    },
    production: false,
    api: {
        url: 'https://localhost:5001/api'
    },
    hubs: {
        edr: 'https://localhost:5001/edr'
    },
    msal: {
        clientID: '2e305521-1e55-42bf-a6ca-aa1ff0d7bff3',
        authority:
            'https://teamincb2c.b2clogin.com/teamincb2c.onmicrosoft.com/B2C_1_signupsignin',
        forgotPasswordAuthority:
            'https://teamincb2c.b2clogin.com/teamincb2c.onmicrosoft.com/B2C_1_forgotpassword',
        redirectUri: 'http://localhost:4200',
        postLogoutRedirectUri: 'http://localhost:4200'
    },
    appInsights: {
        connectionString: 'InstrumentationKey=***AZURE_APP_INSIGHTS_KEY***;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/'
    },
    credo: {
        fileShare: {
            account: 'vmdiagdisk5raqsvzbbo5e6',
            shareName: 'credosoft'
        }
    },
    apm: {
        photoContainer:
            'https://stakrakendev001.blob.core.windows.net/photos/'
    },
    signalR: {
        hubUrl: 'https://signalr-kraken-dev-001.service.signalr.net'
    }
};
