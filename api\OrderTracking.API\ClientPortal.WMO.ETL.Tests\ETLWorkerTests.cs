﻿using System;
using System.Collections.Generic;
using System.Data;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Services;
using ClientPortal.WMO.ETL.Services;
using Google.Cloud.Diagnostics.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NUnit.Framework;

namespace ClientPortal.WMO.ETL.Tests
{
    public class ETLWorkerTests
    {
        private Mock<IConfiguration> _config;
        private Mock<IOptions<Emails>> _emails;
        private Mock<IEmailService> _emailService;
        private Mock<IETLService> _etlService;
        private Mock<ILogger<Worker>> _logger;
        private Mock<IOptions<ZDapperPlus>> _zDapperPlus;
        private Mock<IManagedTracer> _managedTracer;

        [SetUp]
        public void Setup()
        {
            _logger = new Mock<ILogger<Worker>>(MockBehavior.Loose);
            _etlService = new Mock<IETLService>(MockBehavior.Loose);
            _etlService.Setup(service => service.StartJob()).Returns(new OrdersJob
            { Id = Guid.NewGuid().ToString(), StartedUTC = DateTime.UtcNow });
            _emailService = new Mock<IEmailService>(MockBehavior.Loose);
            _config = new Mock<IConfiguration>(MockBehavior.Loose);
            _emails = new Mock<IOptions<Emails>>(MockBehavior.Strict);
            _managedTracer = new Mock<IManagedTracer>(MockBehavior.Strict);
            _emails.SetupGet(emails => emails.Value).Returns(new Emails
            {
                Support = new[]
                {
                    new EmailContact {Email = "EmailOne", GivenName = "NameOne", Surname = "NameOne"},
                    new EmailContact {Email = "EmailTwo", GivenName = "NameTwo", Surname = "NameTwo"}
                }
            });
            _zDapperPlus = new Mock<IOptions<ZDapperPlus>>(MockBehavior.Loose);
        }

        [Test]
        public void CanConstructWorker()
        {
            Assert.DoesNotThrow(() => new Worker(_logger.Object, _managedTracer.Object, _etlService.Object,
                _emailService.Object, _emails.Object, _zDapperPlus.Object));
        }

        [Test]
        public void Worker_StartAsync_HandlesDataException()
        {
            // Arrange 
            _etlService.Setup(service => service.EnsureTargetDatabaseReadyAsync()).Throws<DataException>();
            var worker = new Worker(_logger.Object, _managedTracer.Object, _etlService.Object, _emailService.Object, _emails.Object, _zDapperPlus.Object);
            // Act / Assert 
            Assert.DoesNotThrow(() => worker.DoWork(null));
        }

        [Test]
        public void Worker_doWorkError_CallsSendEmail()
        {
            // Arrange 
            _etlService.Setup(service => service.EnsureTargetDatabaseReadyAsync()).Throws<DataException>();
            var worker = new Worker(_logger.Object, _managedTracer.Object, _etlService.Object, _emailService.Object, _emails.Object, _zDapperPlus.Object);
            // Act 
            worker.DoWork(null);
            _emailService.Verify(service =>
                    service.SendUrgentEmail(It.IsAny<IReadOnlyCollection<UserProfile>>(), It.IsAny<string>(),
                        It.IsAny<string>()),
                Times.AtLeastOnce());
        }
    }
}