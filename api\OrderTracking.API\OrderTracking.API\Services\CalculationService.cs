﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Google.Cloud.Firestore;
using Internal;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using OrderTracking.API.Extensions;
using OrderTracking.API.Interfaces;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services
{
    /// <summary>
    ///     Service class for Calculations
    /// </summary>
    public class CalculationService : ICalculationService
    {
        private readonly CollectionReference _collection;
        private readonly ILogger<CalculationService> _logger;

        /// <summary>
        ///     Constructs the CalculationService class
        ///     instance
        /// </summary>
        /// <param name="options"></param>
        public CalculationService(IFirestoreClientAdapter adapter,IOptions<Connections> options,ILogger<CalculationService> logger)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            _collection = adapter.GetCollection(options.Value.FlangeCalculations);
            _logger = logger;
        }

        /// <summary>
        ///     Add an calculation
        /// </summary>
        /// <param name="calculation"></param>
        /// <returns></returns>
        public async Task<string> AddItemAsync(JObject calculation)
        {
            if (calculation == null) throw new ArgumentNullException(nameof(calculation));

            var calculationObject = calculation.ToObject<Dictionary<string, object>>();


            try
            {
                if (calculationObject["tighteningMethods"] != null)
                {
                    JArray jArray = (JArray)calculationObject["tighteningMethods"];
                    calculationObject["tighteningMethods"] = jArray.Select(x => x.ToString());
                }
                if (calculationObject["toolList"] != null)
                {
                    JArray jArray = (JArray)calculationObject["toolList"];
                    calculationObject["toolList"] = jArray.Select(x => x.ToString());
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occured while trying to cast Enumerable from JArray");
                throw;
            }


            DocumentReference document = await _collection.AddAsync(calculationObject);
            DocumentSnapshot snapshot = await document.GetSnapshotAsync();
            return snapshot.Id;
        }

        /// <summary>
        ///     Delete an calculation
        /// </summary>
        /// <param name="id"></param>
        /// <param name="pk"></param>
        /// <returns></returns>
        public async Task DeleteItemAsync(string id, string pk)
        {
            await _collection.Document(id).DeleteAsync();
        }

        /// <summary>
        ///     Delete multiple calculations
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task DeleteItemsAsync(string[] ids)
        {
            if (ids == null) throw new ArgumentNullException(nameof(ids));

            foreach (var id in ids)
            {
                await _collection.Document(id).DeleteAsync();
            }
        }

        /// <summary>
        ///     Get a calculation
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<dynamic> GetItemAsync(string id)
        {
             var documentSnapshot = await _collection.Document(id).GetSnapshotAsync();
             return documentSnapshot.Exists ? documentSnapshot.ConvertTo<dynamic>() : default;
        }

        /// <summary>
        ///     Get multiple calculations, dependent on the query string
        /// </summary>
        /// <param name="queryString"></param>
        /// <returns></returns>
        public async Task<IEnumerable<dynamic>> GetItemsAsync()
        {
            var querySnapshot = await _collection.GetSnapshotAsync();
            return querySnapshot.Documents.Select(doc => doc.ConvertTo<dynamic>());
        }

        /// <summary>
        ///     Update an Calculation
        /// </summary>
        /// <param name="id"></param>
        /// <param name="calculation"></param>
        /// <returns></returns>
        public async Task UpdateItemAsync(string id, JObject calculation)
        {
            if (calculation == null) throw new ArgumentNullException(nameof(calculation));

            var calculationObject = calculation.ToObject<Dictionary<string, object>>();

            try
            {
                if (calculationObject["tighteningMethods"] != null)
                {
                    JArray jArray = (JArray)calculationObject["tighteningMethods"];
                    calculationObject["tighteningMethods"] = jArray.Select(x => x.ToString());
                }
                if (calculationObject["toolList"] != null)
                {
                    JArray jArray = (JArray)calculationObject["toolList"];
                    calculationObject["toolList"] = jArray.Select(x => x.ToString());
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occured while trying to cast Enumerable from JArray");
                throw;
            }

            await _collection.Document(id).SetAsync(calculationObject);
        }
    }
}