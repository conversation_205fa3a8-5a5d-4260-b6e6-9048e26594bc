import moment from 'moment';
import { AssetCategoryAPICode } from '..';
import { isNullOrUndefined } from '../../../shared/helpers';
import {
    InspectionStatusBreakdown,
    ProjectVm,
    StatusesByBin,
    TaskVM,
    WorkTypePercentages
} from '../view-models';
import {
    FirebaseAsset,
    FirebaseAttribute,
    FirebaseProject,
    FirebaseTask,
    FirebaseWorkOrder,
    ListChangeLog,
    ProjectActivityItem
} from './models';

export function getCurrentEntries(listChangeLog: ListChangeLog) {
    const sorted = Object.values(listChangeLog).sort((a, b) => a.T - b.T);
    let allEntries = Array.from(new Set(sorted.map((x) => x.V)));
    const allCopy = [...allEntries];
    for (const entry of allCopy) {
        const lastMatch = sorted
            .filter((x) => x.V === entry)
            .reduce(
                (prev, curr) =>
                    prev === null || curr.T >= prev.T ? curr : prev,
                null
            );
        if (lastMatch.A === 'Removed')
            allEntries = allEntries.filter((x) => x !== entry);
    }

    allEntries = allEntries.filter((x) => !isNullOrUndefined(x));

    return allEntries;
}

export function calculateAttributeValue<T>(attribute: FirebaseAttribute<T>) {
    return attribute
        ? attribute.Value ??
              (attribute.ValueChangeLog &&
              Object.keys(attribute.ValueChangeLog).length > 0
                  ? Object.values(attribute.ValueChangeLog).reduce(
                        (prev, curr) =>
                            prev === null || curr.T > prev.T ? curr : prev,
                        null
                    ).V
                  : undefined)
        : undefined;
}

export function getEquipmentDescriptionAttribute(
    asset: FirebaseAsset
): FirebaseAttribute<string> {
    if ('Section510_Asset_Walkdown_Details_F' in asset) {
        const identification =
            asset.Section510_Asset_Walkdown_Details_F['SectionIdentification'];
        return identification ? identification['510AW_Q008'] : undefined;
    } else if ('Section570_Asset_Walkdown_Details_F' in asset) {
        const identification =
            asset.Section570_Asset_Walkdown_Details_F['SectionIdentification'];
        return identification ? identification['570AW_Q016'] : undefined;
    } else if ('Section653_Asset_Walkdown_Details_F' in asset) {
        const identification =
            asset.Section653_Asset_Walkdown_Details_F['SectionIdentification'];
        return identification ? identification['653AW_Q004'] : undefined;
    } else return undefined;
}

export function getEquipmentIdAttribute(
    asset: FirebaseAsset
): FirebaseAttribute<string> {
    if ('Section510_Asset_Walkdown_Details_F' in asset) {
        const identification =
            asset.Section510_Asset_Walkdown_Details_F['SectionIdentification'];
        return identification ? identification['510AW_Q006'] : undefined;
    } else if ('Section570_Asset_Walkdown_Details_F' in asset) {
        const identification =
            asset.Section570_Asset_Walkdown_Details_F['SectionIdentification'];
        return identification ? identification['570AW_Q006'] : undefined;
    } else if ('Section653_Asset_Walkdown_Details_F' in asset) {
        const identification =
            asset.Section653_Asset_Walkdown_Details_F['SectionIdentification'];
        return identification ? identification['653AW_Q002'] : undefined;
    } else return undefined;
}

export function lastChangedTime(resource: unknown): number {
    const lastChangedTimes: number[] = [];

    function parseObjectProperties(obj, parse) {
        for (const k in obj) {
            if (obj.hasOwnProperty(k) && k === 'ValueChangeLog') {
                parse(k, obj);
            } else if (typeof obj[k] === 'object' && obj[k] !== null) {
                parseObjectProperties(obj[k], parse);
            }
        }
    }

    parseObjectProperties(resource, function (k: string, prop: unknown) {
        if (k === 'ValueChangeLog') {
            const valueChangeLog = prop[k];
            if (Object.keys(valueChangeLog).length > 0)
                lastChangedTimes.push(
                    Object.values(valueChangeLog).reduce(
                        (prev, curr) =>
                            prev === null || curr['T'] > prev['T']
                                ? curr
                                : prev,
                        null
                    )['T']
                );
        }
    });

    return lastChangedTimes.reduce(
        (prev, curr) => (prev === null || curr > prev ? curr : prev),
        null
    );
}

export function daysBetween(a: Date, b: Date) {
    const oneDay = 1000 * 60 * 60 * 24;
    const aMs = a.getTime();
    const bMs = b.getTime();
    const difference = bMs - aMs;
    return Math.round(difference / oneDay);
}

export function publishedTime(task: FirebaseTask) {
    if (task.Status.Value.toLowerCase() === 'published') {
        const entries = Object.entries(task.Status.ValueChangeLog);
        const latestEntry = entries.reduce(
            (prev, curr) =>
                prev === null || curr[1].T > prev[1].T ? curr : prev,
            null
        );
        return latestEntry[1].T;
    }

    return null;
}

export function fieldWorkCompletedTime(workOrder: FirebaseWorkOrder) {
    if (!workOrder['Field Work Completed']) return null;
    const entries = Object.entries(
        workOrder['Field Work Completed'].ValueChangeLog
    );
    if (entries.length === 0) {
        return null;
    }

    return new Date(
        entries.reduce((prev, curr) =>
            prev === null || new Date(curr[1].T) > new Date(prev[1].T)
                ? curr
                : prev
        )[1].V
    );
}

export namespace Aggregation {
    export function getAssetCategoriesSummary(
        assets: FirebaseAsset[],
        projectIds: string[]
    ) {
        // filter assets to ones that are associated with any of the projectIds
        const projectAssets = assets.filter((asset) =>
            asset.ProjectIds?.some((projectId) =>
                shouldIncludeBasedOnProjectId(projectIds, projectId)
            )
        );

        const total = projectAssets.length;

        // Group assets by asset type
        let assetTypeGroups: { [key: string]: FirebaseAsset[] } =
            projectAssets.reduce((r, a) => {
                const key = a.AssetCategory;
                r[key] = [...(r[key] || []), a];
                return r;
            }, {});

        const result = Object.keys(assetTypeGroups).map((key) => ({
            type: key,
            count: assetTypeGroups[key].length,
            percentage: assetTypeGroups[key].length / total
        }));
        return result;
    }

    export function getSummaryInfo(
        projects: FirebaseProject[],
        projectIds: string[],
        tasks: FirebaseTask[],
        workOrders: FirebaseWorkOrder[]
    ) {
        // CML count
        const fwUTCount = projects
            .filter((project) =>
                shouldIncludeBasedOnProjectId(projectIds, project.id)
            )
            .map((project) => {
                if (project.ProjectActivities) {
                    const addedKeys = getCurrentEntries(
                        project.ProjectActivities.ValueChangeLog
                    );
                    const cmlCount = Object.entries(
                        project.ProjectActivities.Values
                    )
                        .filter((entry) => addedKeys.includes(entry[0]))
                        .map((entry) => entry[1])
                        .filter((value) => value['FW-UT'])
                        .map((cmlActivity) => {
                            const countAttribute = cmlActivity['FW-UT'].Count;
                            const count = countAttribute
                                ? Object.values(
                                      countAttribute.ValueChangeLog
                                  ).reduce(
                                      (prev, curr) =>
                                          prev === null || curr.T > prev.T
                                              ? curr
                                              : prev,
                                      null
                                  ).V
                                : 0;
                            return count;
                        })
                        .reduce((prev, curr) => prev + curr, 0);
                    return cmlCount;
                } else return 0;
            })
            .reduce((prev, curr) => prev + curr, 0);

        const targetReportTimeInDays = 10;

        const projectTasks = tasks.filter((task) =>
            shouldIncludeBasedOnProjectId(projectIds, task.ProjectId)
        );

        // group tasks by workOrderId
        let tasksGroupedByWorkOrder: { [key: string]: FirebaseTask[] } =
            projectTasks.reduce((prev, curr) => {
                const key = curr.WorkOrderId;
                prev[key] = [...(prev[key] || []), curr];
                return prev;
            }, {});

        Object.keys(tasksGroupedByWorkOrder).filter((workOrderId) => {
            if (
                // ignore groups of tasks where not all tasks are marked as published
                tasksGroupedByWorkOrder[workOrderId].some(
                    (task) => task.Status?.Value?.toLowerCase() !== 'published'
                ) ||
                // ignore groups that don't have a field work completed date
                workOrders.find((wo) => wo.id === workOrderId) === undefined ||
                fieldWorkCompletedTime(
                    workOrders.find((wo) => wo.id === workOrderId)
                ) === null ||
                // ignore groups that don't have any tasks with CompletedTime and PublishedTime filled out
                tasksGroupedByWorkOrder[workOrderId].every(
                    (task) => publishedTime(task) === null
                )
            ) {
                delete tasksGroupedByWorkOrder[workOrderId];
            }
        });

        // for each group, find the task with the maximum CompletedTime
        const tasksWithLatestPublishedTime = Object.values(
            tasksGroupedByWorkOrder
        ).map((tasks) =>
            tasks.reduce(
                (prev, curr) =>
                    prev === null || publishedTime(curr) > publishedTime(prev)
                        ? curr
                        : prev,
                null
            )
        );

        // for the task with the maximum CompletedTime, compute PublishedTime - CompletedTime in days
        const daysToPublishPerTask = tasksWithLatestPublishedTime.map((task) =>
            daysBetween(
                fieldWorkCompletedTime(
                    workOrders.find((wo) => wo.id === task.WorkOrderId)
                ),
                new Date(publishedTime(task))
            )
        );

        // average those out between all the tasks this is done for.
        const averageDaysToPublish =
            daysToPublishPerTask.reduce((prev, curr) => prev + curr, 0) /
            daysToPublishPerTask.length;

        return { fwUTCount, targetReportTimeInDays, averageDaysToPublish };
    }

    export function getInspectionStatuses(
        tasks: FirebaseTask[],
        assets: FirebaseAsset[],
        projectIds: string[],
        categories: AssetCategoryAPICode[]
    ): InspectionStatusBreakdown {
        const assetsForCategories = assets.filter(
            (a) =>
                (categories.includes('510') && a.AssetCategory === 'Vessel') ||
                (categories.includes('570') && a.AssetCategory === 'Piping') ||
                (categories.includes('653') && a.AssetCategory === 'Tank')
        );

        const projectTasks = tasks
            .filter(
                (t) =>
                    shouldIncludeBasedOnProjectId(projectIds, t.ProjectId) &&
                    t.TaskType !== 'Asset Walkdown'
            )
            .filter((t) =>
                assetsForCategories.map((a) => a.id).includes(t.AssetId)
            );

        const taskCount = projectTasks.length;

        let inspectionStatuses: { [key: string]: FirebaseTask[] } =
            projectTasks.reduce((previous, current) => {
                if (
                    current?.Status !== undefined &&
                    current.Status.Value === undefined
                ) {
                    const changeLogValues = Object.values(
                        current.Status.ValueChangeLog
                    );
                    // Find the last value of status
                    current.Status.Value =
                        changeLogValues.length > 0
                            ? changeLogValues.reduce((prev, curr) =>
                                  curr.T > prev.T ? curr : prev
                              ).V
                            : undefined;
                }
                const key = current?.Status?.Value ?? '?';
                previous[key] = [...(previous[key] || []), current];
                return previous;
            }, {});

        const statusCounts = Object.keys(inspectionStatuses).map((key) => ({
            status: key,
            count: inspectionStatuses[key].length
        }));
        return { taskCount, inspectionStatuses: statusCounts };
    }

    export function getTimelineTasksAndProjects(
        tasks: FirebaseTask[],
        projectIds: string[],
        assets: FirebaseAsset[],
        projects: FirebaseProject[],
        workOrders: FirebaseWorkOrder[]
    ): [TaskVM[], ProjectVm[]] {
        // Make sure task status is calculated
        tasks.forEach((task) => {
            if (task.Status)
                task.Status.Value = calculateAttributeValue(task.Status);
            else task.Status = { ValueChangeLog: {} };
        });
        const projectTasks = tasks
            .filter((task) =>
                projectIds?.length > 0
                    ? projectIds.includes(task.ProjectId)
                    : true
            )
            // filter out published tasks from the timeline
            .filter((task) => task.Status?.Value?.toLowerCase() !== 'published')
            // filter out tasks without due date
            .filter((task) => calculateAttributeValue(task['Due Date']))
            .map((task) => {
                const asset = assets.find((asset) => asset.id === task.AssetId);
                const project = projects.find(
                    (project) => project.id === task.ProjectId
                );
                const workOrder = workOrders.find(
                    (workOrder) => workOrder.id === task.WorkOrderId
                );

                const equipmentId = calculateAttributeValue(
                    getEquipmentIdAttribute(asset)
                );

                const equipmentDescription = calculateAttributeValue(
                    getEquipmentDescriptionAttribute(asset)
                );

                const plannedStartValue = calculateAttributeValue(
                    task['Planned Start']
                );
                const plannedEndValue = calculateAttributeValue(
                    task['Planned End']
                );
                const updatedAt = lastChangedTime(task);
                const dueDate = calculateAttributeValue(task['Due Date']);
                return {
                    id: task.id,
                    projectId: task.ProjectId,
                    equipmentId: equipmentId,
                    area: calculateAttributeValue(asset.Area),
                    unit: calculateAttributeValue(asset.Unit),
                    equipmentType: asset.AssetCategory,
                    equipmentDescription: equipmentDescription,
                    dueDate: dueDate
                        ? new Date(dueDate.replace(/-/g, '/')).toString()
                        : undefined,
                    taskType: task.TaskType,
                    assignedUsers: task.AssignedUsers,
                    taskUpdatedDate:
                        updatedAt > 0
                            ? new Date(updatedAt).toString()
                            : undefined,
                    afe: calculateAttributeValue(task['Purchase Order/AFE']),
                    supervisor: calculateAttributeValue(task.Supervisor),
                    plannedStart: plannedStartValue
                        ? new Date(
                              plannedStartValue.replace(/-/g, '/')
                          ).toString()
                        : undefined,
                    plannedEnd: plannedEndValue
                        ? new Date(
                              plannedEndValue.replace(/-/g, '/')
                          ).toString()
                        : undefined,
                    status: calculateAttributeValue(task.Status) ?? 'Unknown',
                    apmProjectNumber: calculateAttributeValue(
                        project.AccountingDetails['APM Project Number']
                    ),
                    apmWorkOrderNumber: calculateAttributeValue(
                        workOrder['APM Work Order Number']
                    ),
                    projectName: calculateAttributeValue(project.Name),
                    teamProjectNumber: calculateAttributeValue(
                        project.AccountingDetails['TEAM Project Number']
                    ),
                    apmTaskNumber: calculateAttributeValue(
                        task['Task APM Number']
                    ),
                    client: 'Chevron',
                    workOrderId: workOrder.id,
                    clientWorkOrderNumber: calculateAttributeValue(
                        task['Client Work Order Number']
                    ),
                    businessUnitId: task.BusinessUnitId.Value
                } as TaskVM;
            });

        const filteredProjects = projects
            .filter((project) => projectIds.includes(project.id))
            .map(
                (project) =>
                    ({
                        name: calculateAttributeValue(project.Name),
                        id: project.id
                    } as ProjectVm)
            );
        return [projectTasks, filteredProjects];
    }

    export function getStatusesByMonth(
        projectIds: string[],
        tasks: FirebaseTask[],
        assets: FirebaseAsset[]
    ) {
        const monthMap: {
            [key: string]: { [key: string]: { [key: string]: number } };
        } = {};
        const statusesByMonth: StatusesByBin[] = [];

        if (projectIds && projectIds.length > 0) {
            tasks = tasks.filter((task) => projectIds.includes(task.ProjectId));
        }

        // loop through tasks and establish bins
        for (const task of tasks) {
            const asset = assets.find((a) => a.id === task.AssetId);
            let bin: string;
            const dateParsed = new Date(
                calculateAttributeValue(task['Due Date'])
            );

            const momentOfDueDate = moment(
                calculateAttributeValue(task['Due Date']),
                'YYYY-M-D'
            );

            if (momentOfDueDate.isValid) {
                bin =
                    momentOfDueDate.get('month') +
                    1 +
                    '-' +
                    momentOfDueDate.get('year');
            } else continue;

            let status = calculateAttributeValue(task.Status) ?? '?';
            status =
                status !== 'Completed' &&
                status !== 'On Hold' &&
                status !== 'Canceled' &&
                Date.now() > dateParsed.getTime()
                    ? 'Overdue'
                    : status;

            // check to see if bin has been encountered
            if (monthMap[bin]) {
                const assetTypeMap = monthMap[bin];

                if (assetTypeMap[asset.AssetCategory]) {
                    const statusMap = assetTypeMap[asset.AssetCategory];
                    if (statusMap[status]) {
                        statusMap[status] += 1;
                    } else {
                        statusMap[status] = 1;
                    }
                } else {
                    const statusMap: { [key: string]: number } = {};
                    statusMap[status] = 1;
                    assetTypeMap[asset.AssetCategory] = statusMap;
                }
            } else {
                // if have not seen this bin yet, must go through the entire map to add the first status count
                const assetTypeMap: {
                    [key: string]: { [key: string]: number };
                } = {};
                const statusMap: { [key: string]: number } = {};
                statusMap[status] = 1;
                assetTypeMap[asset.AssetCategory] = statusMap;
                monthMap[bin] = assetTypeMap;
            }
        }

        // loop through keys to create proper array of histogram objects
        for (const key in monthMap) {
            const countsByMonth = new StatusesByBin(key);
            const assetTypeMap = monthMap[key];

            for (const assetType in assetTypeMap) {
                const statusMap = assetTypeMap[assetType];
                switch (assetType) {
                    case 'Piping':
                        for (const status in statusMap) {
                            switch (status) {
                                case 'Scheduled':
                                    countsByMonth.pipingScheduledCount =
                                        statusMap[status];
                                    break;
                                case 'In Progress':
                                    countsByMonth.pipingInProgressCount =
                                        statusMap[status];
                                    break;
                                case 'Completed':
                                    countsByMonth.pipingCompleteCount =
                                        statusMap[status];
                                    break;
                                case 'Not Started':
                                    countsByMonth.pipingNotStartedCount =
                                        statusMap[status];
                                case 'Published':
                                    countsByMonth.pipingPublishedCount =
                                        statusMap[status];
                                    break;
                                case 'On Hold':
                                    countsByMonth.pipingOnHoldCount =
                                        statusMap[status];
                                    break;
                                case 'Overdue':
                                    countsByMonth.pipingOverdueCount =
                                        statusMap[status];
                                    break;
                                case 'Canceled':
                                    countsByMonth.pipingCanceledCount =
                                        statusMap[status];
                                    break;
                                case '?':
                                    countsByMonth.pipingUnknownCount =
                                        statusMap[status];
                                    break;
                            }
                        }
                        break;
                    case 'Tank':
                        for (const status in statusMap) {
                            switch (status) {
                                case 'Scheduled':
                                    countsByMonth.tankScheduledCount =
                                        statusMap[status];
                                    break;
                                case 'In Progress':
                                    countsByMonth.tankInProgressCount =
                                        statusMap[status];
                                    break;
                                case 'Completed':
                                    countsByMonth.tankCompleteCount =
                                        statusMap[status];
                                    break;
                                case 'Not Started':
                                    countsByMonth.tankNotStartedCount =
                                        statusMap[status];
                                    break;
                                case 'Published':
                                    countsByMonth.tankPublishedCount =
                                        statusMap[status];
                                    break;
                                case 'On Hold':
                                    countsByMonth.tankOnHoldCount =
                                        statusMap[status];
                                    break;
                                case 'Overdue':
                                    countsByMonth.tankOverdueCount =
                                        statusMap[status];
                                    break;
                                case 'Canceled':
                                    countsByMonth.tankCanceledCount =
                                        statusMap[status];
                                    break;
                                case '?':
                                    countsByMonth.tankUnknownCount =
                                        statusMap[status];
                                    break;
                            }
                        }
                        break;
                    case 'Vessel':
                        for (const status in statusMap) {
                            switch (status) {
                                case 'Scheduled':
                                    countsByMonth.vesselScheduledCount =
                                        statusMap[status];
                                    break;
                                case 'In Progress':
                                    countsByMonth.vesselInProgressCount =
                                        statusMap[status];
                                    break;
                                case 'Completed':
                                    countsByMonth.vesselCompleteCount =
                                        statusMap[status];
                                    break;
                                case 'Not Started':
                                    countsByMonth.vesselNotStartedCount =
                                        statusMap[status];
                                    break;
                                case 'Published':
                                    countsByMonth.vesselPublishedCount =
                                        statusMap[status];
                                    break;
                                case 'On Hold':
                                    countsByMonth.vesselOnHoldCount =
                                        statusMap[status];
                                    break;
                                case 'Overdue':
                                    countsByMonth.vesselOverdueCount =
                                        statusMap[status];
                                    break;
                                case 'Canceled':
                                    countsByMonth.vesselCanceledCount =
                                        statusMap[status];
                                    break;
                                case '?':
                                    countsByMonth.vesselUnknownCount =
                                        statusMap[status];
                                    break;
                            }
                        }
                        break;
                }
            }
            statusesByMonth.push(countsByMonth);
        }

        // sort dates
        statusesByMonth.sort((a, b) => {
            if (a.bin === b.bin) return 0;
            const aDate = a.bin.split('-');
            const bDate = b.bin.split('-');
            const aMonth = Number(aDate[0]);
            const aYear = Number(aDate[1]);
            const bMonth = Number(bDate[0]);
            const bYear = Number(bDate[1]);
            if (aYear < bYear) return -1;
            if (bYear < aYear) return 1;
            if (aMonth < bMonth) return -1;
            if (bMonth < aMonth) return 1;
            return 0;
        });

        // now change bins to easier to read format
        for (const entry of statusesByMonth) {
            const date = entry.bin.split('-');
            switch (date[0]) {
                case '1':
                    entry.bin = 'Jan ' + date[1];
                    break;
                case '2':
                    entry.bin = 'Feb ' + date[1];
                    break;
                case '3':
                    entry.bin = 'March ' + date[1];
                    break;
                case '4':
                    entry.bin = 'April ' + date[1];
                    break;
                case '5':
                    entry.bin = 'May ' + date[1];
                    break;
                case '6':
                    entry.bin = 'June ' + date[1];
                    break;
                case '7':
                    entry.bin = 'July ' + date[1];
                    break;
                case '8':
                    entry.bin = 'Aug ' + date[1];
                    break;
                case '9':
                    entry.bin = 'Sept ' + date[1];
                    break;
                case '10':
                    entry.bin = 'Oct ' + date[1];
                    break;
                case '11':
                    entry.bin = 'Nov ' + date[1];
                    break;
                case '12':
                    entry.bin = 'Dec ' + date[1];
                    break;
            }
        }

        return statusesByMonth;
    }

    export function getStatusesByWeek(
        projectIds: string[],
        tasks: FirebaseTask[],
        assets: FirebaseAsset[]
    ) {
        const weekMap: {
            [key: string]: { [key: string]: { [key: string]: number } };
        } = {};
        const statusesByWeek: StatusesByBin[] = [];

        if (projectIds && projectIds.length > 0) {
            tasks = tasks.filter((task) => projectIds.includes(task.ProjectId));
        }

        function getWeekNumber(date: Date) {
            const dayOne = new Date(date.getFullYear(), 0, 1);
            const numberOfDays = Math.floor(
                (date.valueOf() - dayOne.valueOf()) / (24 * 60 * 60 * 1000)
            );
            const result = Math.ceil((date.getDay() + 1 + numberOfDays) / 7);
            return result;
        }

        // loop through tasks and establish bins
        for (const task of tasks) {
            const asset = assets.find((a) => a.id === task.AssetId);
            let bin: string;
            const dateParsed = new Date(
                calculateAttributeValue(task['Due Date'])
            );
            if (dateParsed && dateParsed.toString() !== 'Invalid Date') {
                bin =
                    getWeekNumber(dateParsed) + '-' + dateParsed.getFullYear();
            } else continue;

            let status = calculateAttributeValue(task.Status) ?? '?';
            status =
                status !== 'Completed' &&
                status !== 'On Hold' &&
                status !== 'Canceled' &&
                Date.now() > dateParsed.getTime()
                    ? 'Overdue'
                    : status;

            if (weekMap[bin]) {
                const assetTypeMap = weekMap[bin];
                if (assetTypeMap[asset.AssetCategory]) {
                    const statusMap = assetTypeMap[asset.AssetCategory];
                    if (statusMap[status]) {
                        statusMap[status] += 1;
                    } else {
                        statusMap[status] = 1;
                    }
                } else {
                    const statusMap: { [key: string]: number } = {};
                    statusMap[status] = 1;
                    assetTypeMap[asset.AssetCategory] = statusMap;
                }
            } else {
                // if have not seen bin yet, must go through the entire map to add the first status count
                const assetTypeMap: {
                    [key: string]: { [key: string]: number };
                } = {};
                const statusMap: { [key: string]: number } = {};
                statusMap[status] = 1;
                assetTypeMap[asset.AssetCategory] = statusMap;
                weekMap[bin] = assetTypeMap;
            }
        }
        // loop through keys to create proper array of histogram objects
        for (const key in weekMap) {
            const countsByWeek = new StatusesByBin(key);
            const assetTypeMap = weekMap[key];
            for (const assetType in assetTypeMap) {
                const statusMap = assetTypeMap[assetType];
                switch (assetType) {
                    case 'Piping':
                        for (const status in statusMap) {
                            switch (status) {
                                case 'Scheduled':
                                    countsByWeek.pipingScheduledCount =
                                        statusMap[status];
                                    break;
                                case 'In Progress':
                                    countsByWeek.pipingInProgressCount =
                                        statusMap[status];
                                    break;
                                case 'Completed':
                                    countsByWeek.pipingCompleteCount =
                                        statusMap[status];
                                    break;
                                case 'Not Started':
                                    countsByWeek.pipingNotStartedCount =
                                        statusMap[status];
                                    break;
                                case 'Published':
                                    countsByWeek.pipingPublishedCount =
                                        statusMap[status];
                                    break;
                                case 'On Hold':
                                    countsByWeek.pipingOnHoldCount =
                                        statusMap[status];
                                    break;
                                case 'Canceled':
                                    countsByWeek.pipingCanceledCount =
                                        statusMap[status];
                                    break;
                                case 'Overdue':
                                    countsByWeek.pipingOverdueCount =
                                        statusMap[status];
                                    break;
                                case '?':
                                    countsByWeek.pipingUnknownCount =
                                        statusMap[status];
                                    break;
                            }
                        }
                        break;
                    case 'Tank':
                        for (const status in statusMap) {
                            switch (status) {
                                case 'Scheduled':
                                    countsByWeek.tankScheduledCount =
                                        statusMap[status];
                                    break;
                                case 'In Progress':
                                    countsByWeek.tankInProgressCount =
                                        statusMap[status];
                                    break;
                                case 'Completed':
                                    countsByWeek.tankCompleteCount =
                                        statusMap[status];
                                    break;
                                case 'Not Started':
                                    countsByWeek.tankNotStartedCount =
                                        statusMap[status];
                                    break;
                                case 'Published':
                                    countsByWeek.tankPublishedCount =
                                        statusMap[status];
                                    break;
                                case 'On Hold':
                                    countsByWeek.tankOnHoldCount =
                                        statusMap[status];
                                    break;
                                case 'Canceled':
                                    countsByWeek.tankCanceledCount =
                                        statusMap[status];
                                    break;
                                case 'Overdue':
                                    countsByWeek.tankOverdueCount =
                                        statusMap[status];
                                    break;
                                case '?':
                                    countsByWeek.tankUnknownCount =
                                        statusMap[status];
                                    break;
                            }
                        }
                        break;
                    case 'Vessel':
                        for (const status in statusMap) {
                            switch (status) {
                                case 'Scheduled':
                                    countsByWeek.vesselScheduledCount =
                                        statusMap[status];
                                    break;
                                case 'In Progress':
                                    countsByWeek.vesselInProgressCount =
                                        statusMap[status];
                                    break;
                                case 'Completed':
                                    countsByWeek.vesselCompleteCount =
                                        statusMap[status];
                                    break;
                                case 'Not Started':
                                    countsByWeek.vesselNotStartedCount =
                                        statusMap[status];
                                    break;
                                case 'Published':
                                    countsByWeek.vesselPublishedCount =
                                        statusMap[status];
                                    break;
                                case 'On Hold':
                                    countsByWeek.vesselOnHoldCount =
                                        statusMap[status];
                                    break;
                                case 'Canceled':
                                    countsByWeek.vesselCanceledCount =
                                        statusMap[status];
                                    break;
                                case 'Overdue':
                                    countsByWeek.vesselOverdueCount =
                                        statusMap[status];
                                    break;
                                case '?':
                                    countsByWeek.vesselUnknownCount =
                                        statusMap[status];
                                    break;
                            }
                        }
                        break;
                }
            }

            statusesByWeek.push(countsByWeek);
        }

        // sort dates
        statusesByWeek.sort((a, b) => {
            if (a.bin === b.bin) return 0;

            const aDate = a.bin.split('-');
            const bDate = b.bin.split('-');
            const aWeek = Number(aDate[0]);
            const bWeek = Number(bDate[0]);
            const aYear = Number(aDate[1]);
            const bYear = Number(bDate[1]);

            if (aYear < bYear) return -1;
            if (bYear < aYear) return 1;
            if (aWeek < bWeek) return -1;
            if (bWeek < aWeek) return 1;
            return 0;
        });

        // now change bins to easier to read format
        for (const entry of statusesByWeek) {
            entry.bin = `Week ${entry.bin}`;
        }

        return statusesByWeek;
    }

    export function getAssetsByAreaAndType(
        projectIds: string[],
        assets: FirebaseAsset[]
    ) {
        if (projectIds && projectIds.length > 0) {
            assets = assets.filter((asset) => {
                if (isNullOrUndefined(asset.ProjectIds)) return false;

                return asset.ProjectIds.some((id) => projectIds.includes(id));
            });
        }

        let assetAreaGroups: { [key: string]: FirebaseAsset[] } = assets.reduce(
            (prev, curr) => {
                const key = calculateAttributeValue(curr.Area) ?? '?';
                prev[key] = [...(prev[key] || []), curr];
                return prev;
            },
            {}
        );

        const result = Object.entries(assetAreaGroups).map(
            ([area, groupAssets]) => ({
                area,
                vessel: groupAssets.filter((a) => a.AssetCategory === 'Vessel')
                    .length,
                tank: groupAssets.filter((a) => a.AssetCategory === 'Tank')
                    .length,
                piping: groupAssets.filter((a) => a.AssetCategory === 'Piping')
                    .length
            })
        );
        return result;
    }

    export function createInspectionsWithoutDueDates(
        projectIds: string[],
        tasks: FirebaseTask[],
        assets: FirebaseAsset[]
    ) {
        if (projectIds && projectIds.length > 0) {
            tasks = tasks.filter((task) => projectIds.includes(task.ProjectId));
        }

        // Filter out tasks that have due dates
        tasks = tasks
            .map((task) => {
                if (task['Due Date'])
                    task['Due Date'].Value = calculateAttributeValue(
                        task['Due Date']
                    );
                return task;
            })
            .filter(
                (task) =>
                    isNullOrUndefined(task['Due Date']) ||
                    isNullOrUndefined(task['Due Date'].Value)
            );

        const totalTaskCount = tasks.length;

        // group by asset category
        const assetCategoryGroups: { [key: string]: FirebaseTask[] } =
            tasks.reduce((prev, curr) => {
                const asset = assets.find((a) => a.id === curr.AssetId);
                if (isNullOrUndefined(asset)) {
                    console.warn('uh oh');
                }
                const key = asset.AssetCategory;
                prev[key] = [...(prev[key] || []), curr];
                return prev;
            }, {});

        const result = Object.entries(assetCategoryGroups).map(
            ([assetCategory, groupTasks]) => ({
                assetCategory,
                percent: groupTasks.length / totalTaskCount,
                count: groupTasks.length
            })
        );
        return result;
    }

    export function getActivitySummary(
        projectIds: string[],
        projects: FirebaseProject[],
        tasks: FirebaseTask[]
    ) {
        if (projectIds?.length > 0) {
            projects = projects.filter((project) =>
                projectIds.includes(project.id)
            );
            tasks = tasks.filter((task) => projectIds.includes(task.ProjectId));
        }

        const projectActivities = projects
            .filter(
                (project) =>
                    project.ProjectActivities &&
                    project.ProjectActivities.Values
            )
            .flatMap((project) => {
                const currentEntryKeys = getCurrentEntries(
                    project.ProjectActivities.ValueChangeLog
                );
                return Object.entries(project.ProjectActivities.Values)
                    .filter(([key, value]) => currentEntryKeys.includes(key))
                    .map((valueEntry) => valueEntry[1]);
            });

        const taskActivities = tasks
            .filter(
                (task) =>
                    task['Activity Tracker'] && task['Activity Tracker'].Values
            )
            .flatMap((task) => {
                const currentEntryKeys = getCurrentEntries(
                    task['Activity Tracker'].ValueChangeLog
                );
                return Object.entries(task['Activity Tracker'].Values)
                    .filter(([key, value]) => currentEntryKeys.includes(key))
                    .map((valueEntry) => valueEntry[1]);
            });

        const projectDurations = projectActivities
            .flatMap(
                (activity) =>
                    Object.entries(activity).filter(
                        ([key, _]) =>
                            ![
                                'Date',
                                'User',
                                'id',
                                'Client WO Number'
                            ].includes(key)
                    ) as [string, ProjectActivityItem][]
            )
            .map(([key, value]) => ({
                name: key,
                duration: calculateAttributeValue(value['Duration'])
            }));

        const taskDurations = taskActivities
            .flatMap(
                (activity) =>
                    Object.entries(activity).filter(
                        ([key, _]) => !['Date', 'id'].includes(key)
                    ) as [string, FirebaseAttribute<number>][]
            )
            .map(([key, value]) => ({
                name: key,
                duration: calculateAttributeValue(value)
            }));

        return WorkTypePercentages.createFrom([
            ...projectDurations,
            ...taskDurations
        ]);
    }

    /**
     * Takes a list of projectIds to filter to and determines if the projectId provided
     * as the second argument should be considered included.  If projectIds is empty,
     * no restriction is imposed.
     * @param projectIds projectIds to filter to (if empty, no restriction)
     * @param projectId projectId of resource
     * @returns whether projectId should be included
     */
    function shouldIncludeBasedOnProjectId(
        projectIds: string[],
        projectId: string
    ) {
        return (
            (projectIds?.length > 0 && projectIds.includes(projectId)) ||
            projectIds === undefined ||
            projectIds === null ||
            projectIds?.length === 0
        );
    }
}
