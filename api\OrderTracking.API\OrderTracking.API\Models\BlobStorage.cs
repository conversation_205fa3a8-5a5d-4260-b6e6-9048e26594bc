namespace OrderTracking.API.Models
{
    /// <summary>
    ///     Model to represent the Azure Blob Storage app settings
    /// </summary>
    public class BlobStorage
    {
        /// <summary>
        ///     Endpoint for Azure Blob Storage Container service
        /// </summary>
        public string BlobEndpoint { get; set; }

        /// <summary>
        ///     Name of account for Container service
        /// </summary>
        public string AccountName { get; set; }

        /// <summary>
        ///     The connection string to the Container service
        /// </summary>
        public string ConnectionString { get; set; }

        /// <summary>
        ///     The Key for the Container service
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        ///     The name of the folder for WMO related files
        /// </summary>
        public string WMOContainer { get; set; }

        /// <summary>
        ///     The name of the folder for EDR related files
        /// </summary>
        public string EDRContainer { get; set; }

        /// <summary>
        ///     The name of the folder for APM related files
        /// </summary>
        public string APMContainer { get; set; }

        /// <summary>
        ///     The name of the container for APM Report file (temporary blobs with anonymous read access)
        /// </summary>
        public string APMReportingContainer { get; set; }

        /// <summary>
        ///     The key for accessing apm containers
        /// </summary>
        public string APMKey{ get; set; }

        /// <summary>
        ///     The key for letting jaspersoft access the apm reporting container
        /// </summary>
        public string APMReportingKey { get; set; }

        /// <summary>
        /// APM Cloud Storage ProjectID
        /// </summary>
        public string APMCloudStorageProjectId { get; set; }

        /// <summary>
        /// AMP Cloud Storage Bucket Name
        /// </summary>
        public string APMCloudStorageBucketName { get; set; }

        /// <summary>
        /// AMP Workorder Cloud Storage Bucket Name
        /// </summary>
        public string APMWOCloudStorageBucketName { get; set; }

        /// <summary>
        /// APM Service Credentials SecretId
        /// </summary>
        public string APMServiceCredentialsSecretId { get; set; }
    }
}