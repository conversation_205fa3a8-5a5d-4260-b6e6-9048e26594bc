using ClientPortal.Shared.Models;
using ClientPortal.Shared.Services;
using ClientPortal.WMO.ETL.Services;
using Google.Cloud.Diagnostics.AspNetCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace ClientPortal.WMO.ETL
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        // ReSharper disable once MemberCanBePrivate.Global
        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            return Host.CreateDefaultBuilder(args)
#if DEBUG
                .ConfigureAppConfiguration(config => { config.AddUserSecrets("dotnet-ClientPortal.WMO.ETL-105ABC9F-7C87-4AF8-818E-068AAFF599CA"); })
#endif
                .ConfigureServices((hostContext, services) =>
                {
                    var sendGridConfig = hostContext.Configuration.GetSection(nameof(Shared.Models.SendGrid));
                    services.AddSingleton<IETLService, ETLService>();
                    services.AddHostedService<Worker>();
                    services.AddGoogleDiagnosticsForAspNetCore();
                    services.Configure<Shared.Models.SendGrid>(sendGridConfig);
                    services.AddScoped<IEmailService, EmailService>();
                    services.AddScoped<IETLSettingsService, ETLSettingsService>();
                    services.Configure<Emails>(hostContext.Configuration.GetSection(nameof(Emails)));
                    services.Configure<ZDapperPlus>(hostContext.Configuration.GetSection(nameof(ZDapperPlus)));
                });
        }

    }
}