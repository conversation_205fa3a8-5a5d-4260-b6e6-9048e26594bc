﻿using System;
using Google.Cloud.Firestore;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Configuration;

namespace OrderTracking.API.Repositories
{
    public sealed class ContainerFactory : IContainerFactory
    {
        #region Fields and Constants

        private readonly ValidatingFirestoreClient _client;
        private readonly string _database;

        private readonly string _rolesContainer;

        private readonly string _usersContainer;

        #endregion

        #region Constructors

        public ContainerFactory(IConfigurationSection configurationSection, ValidatingFirestoreClient firestoreClient)
        {
            if (configurationSection == null) throw new ArgumentNullException(nameof(configurationSection));
            
            //Get the database name and override defaults if appropriate
            _database = configurationSection.GetSection("Database").Value;
            _rolesContainer = configurationSection.GetSection("Roles").Value;
            _usersContainer = configurationSection.GetSection("UserProfiles").Value;

            _client = firestoreClient;
        }

        #endregion

        #region Interface Implementation

        public CollectionReference CreateCollection<T>(out string partitionKeyPath) where T : IFirestoreRepository
        {
            var t = typeof(T);
            if (typeof(IRolesRepository).IsAssignableFrom(t))
            {
                partitionKeyPath = null; // Firestore does not have a concept of partition key path like Cosmos DB
                return _client.GetCollection(_rolesContainer, out partitionKeyPath);
            }
            if (typeof(IUserProfileRepository).IsAssignableFrom(t))
            {
                partitionKeyPath = null; // Firestore does not have a concept of partition key path like Cosmos DB
                return _client.GetCollection(_usersContainer, out partitionKeyPath);
            }

            partitionKeyPath = null;
            return null;
        }

        #endregion
    }
}