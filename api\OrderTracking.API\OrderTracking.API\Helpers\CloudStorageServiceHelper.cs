﻿using Azure.Security.KeyVault.Secrets;
using Azure.Identity;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.CodeAnalysis;

namespace OrderTracking.API.Helpers
{
    /// <summary>
    /// Azure Key Vault Helper for Cloud Storage Service
    /// </summary>
    public class CloudStorageServiceHelper
    {
        /// <summary>
        /// Get secret from Azure Key Vault
        /// </summary>
        /// <param name="keyVaultName">Azure Key Vault name (replaces projectId)</param>
        /// <param name="secretName">Secret name (replaces secretId)</param>
        /// <returns></returns>
        public static string GetSecret(string keyVaultName, string secretName)
        {
            var keyVaultUri = new Uri($"https://{keyVaultName}.vault.azure.net/");
            var credential = new DefaultAzureCredential();
            var client = new SecretClient(keyVaultUri, credential);

            KeyVaultSecret secret = client.GetSecret(secretName);
            return secret.Value;
        }
    }
}
