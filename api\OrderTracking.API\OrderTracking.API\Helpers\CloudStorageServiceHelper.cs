﻿using Google.Cloud.SecretManager.V1;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.CodeAnalysis;

namespace OrderTracking.API.Helpers
{
    /// <summary>
    /// 
    /// </summary>
    public class CloudStorageServiceHelper
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="secretId"></param>
        /// <returns></returns>
        public static string GetSecret(string projectId, string secretId)
        {
            SecretManagerServiceClient client = SecretManagerServiceClient.Create();
            SecretVersionName secretVersionName = new SecretVersionName(projectId, secretId, "latest");
            AccessSecretVersionResponse result = client.AccessSecretVersion(secretVersionName);

            // Extract the secret payload as a string
            string secretPayload = result.Payload.Data.ToStringUtf8();
            return secretPayload;
        }
    }
}
