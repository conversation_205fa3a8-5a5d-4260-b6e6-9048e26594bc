using System;
using Google.Cloud.Firestore;
using Newtonsoft.Json;

namespace OrderTracking.API.Models
{
    /// <summary>
    ///     Release notes model for OneInsight
    /// </summary>
    [FirestoreData]
    public class ReleaseNotes
    {
        /// <summary>
        ///     Unique identifier for release notes record
        /// </summary>
        [JsonProperty("id")]
        [FirestoreDocumentId]
        public string Id { get; set; }

        /// <summary>
        ///     The email address of the user who created the release notes record
        /// </summary>
        [JsonProperty("createdBy")]
        [FirestoreProperty("createdBy")]
        public string CreatedBy { get; set; }

        /// <summary>
        ///     The release notes contents
        /// </summary>
        [JsonProperty("notes")]
        [FirestoreProperty("notes")]
        public string Notes { get; set; }

        /// <summary>
        ///     When the release note was created.
        /// </summary>
        [JsonProperty("createdAt")]
        [FirestoreDocumentCreateTimestamp]
        public DateTime CreatedAt { get; set; }
    }
}