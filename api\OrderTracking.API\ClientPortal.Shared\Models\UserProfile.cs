using System;
using System.Collections.Generic;
using Dapper.Contrib.Extensions;
using Newtonsoft.Json;
using Google.Cloud.Firestore;


namespace ClientPortal.Shared.Models
{
    [FirestoreData]
    public class UserProfile : IFirestoreEntity<string>, IEmailRecipient
    {
        private const string ClientDisclaimerVersion = "3";
        private const string TEAMDisclaimerVersion = "2";

        #region Properties

        // TODO: Test This
        [FirestoreProperty("active")]
        [JsonProperty(PropertyName = "active")]

        public bool Active
        {
            get
            {
                if (IsTeamEmployee) return true;

                if (LastVerificationDate == null) return false;

                var oneMonthTimeSpan = new TimeSpan(30, 0, 0, 0);

                if (DateTime.UtcNow - LastVerificationDate > oneMonthTimeSpan) return false;

                return true;
            }
        }

        [FirestoreProperty("acceptedAgreement")]
        [JsonProperty(PropertyName = "acceptedAgreement")]
        public bool AcceptedAgreement
        {
            get
            {
                var oneYearTimeSpan = new TimeSpan(365, 0, 0, 0);
                if (!IsTeamEmployee)
                    return AcceptedClientDisclaimerVersion == ClientDisclaimerVersion &&
                           DateTime.UtcNow - AcceptedDisclaimerDate < oneYearTimeSpan;
                return AcceptedTeamDisclaimerVersion == TEAMDisclaimerVersion &&
                       DateTime.UtcNow - AcceptedDisclaimerDate < oneYearTimeSpan;
            }
        }

        [FirestoreProperty("isTeamEmployee")]
        [JsonProperty(PropertyName = "isTeamEmployee")]
        [Write(false)]
        public bool IsTeamEmployee
        {
            get
            {
                if (Id == null)
                    return false;

                if (Id.ToLower().EndsWith("@teaminc.com")) return true;

                return false;
            }
        }

        [FirestoreProperty("acceptedClientDisclaimerVersion")]
        [JsonProperty(PropertyName = "acceptedClientDisclaimerVersion")]
        public string AcceptedClientDisclaimerVersion { get; set; }


        [FirestoreProperty("acceptedTeamDisclaimerVersion")]
        [JsonProperty(PropertyName = "acceptedTeamDisclaimerVersion")]
        public string AcceptedTeamDisclaimerVersion { get; set; }

        [FirestoreProperty("acceptedDisclaimerDate")]
        [JsonProperty(PropertyName = "acceptedDisclaimerDate")]
        public DateTime AcceptedDisclaimerDate { get; set; }

        [FirestoreProperty("customerAccounts")]
        [JsonProperty(PropertyName = "customerAccounts")]
        public ICollection<string> CustomerAccounts { get; set; } = new List<string>();

        [FirestoreProperty("districtIds")]
        [JsonProperty(PropertyName = "districtIds")]
        public ICollection<string> DistrictIds { get; set; } = new List<string>();
        [FirestoreProperty("assetManagementSiteIds")]
        [JsonProperty(PropertyName = "assetManagementSiteIds")]
        public ICollection<string> AssetManagementSiteIds { get; set; } = new List<string>();

        [FirestoreProperty("remoteMonitoringSiteIds")]
        [JsonProperty(PropertyName = "remoteMonitoringSiteIds")]
        public ICollection<string> RemoteMonitoringSiteIds { get; set; } = new List<string>();

        [FirestoreProperty("email")]
        [JsonProperty(PropertyName = "email")]
        public string Email { get; set; }

        [FirestoreProperty("givenName")]
        [JsonProperty(PropertyName = "givenName")]
        public string GivenName { get; set; }


        [FirestoreDocumentId]
        [JsonProperty(PropertyName = "id")]
        public string Id { get; set; }


        [FirestoreProperty("name")]
        [JsonProperty(PropertyName = "name")]
        public string Name { get; set; }

        [FirestoreProperty("roles")]
        [JsonProperty(PropertyName = "roles")]
        public ICollection<string> Roles { get; set; } = new List<string>();

        [FirestoreProperty("surname")]
        [JsonProperty(PropertyName = "surname")]
        public string Surname { get; set; }

        [FirestoreProperty("lastLoginDate")]
        [JsonProperty(PropertyName = "lastLoginDate")]
        public DateTime? LastLoginDate { get; set; }

        [FirestoreProperty("lastVerificationDate")]
        [JsonProperty(PropertyName = "lastVerificationDate")]
        public DateTime? LastVerificationDate { get; set; }

        [FirestoreProperty("verificationToken")]
        [JsonProperty(PropertyName = "verificationToken")]
        public string VerificationToken { get; set; }

        [FirestoreProperty("lastClientPortalVersion")]
        [JsonProperty(PropertyName = "lastClientPortalVersion")]
        public string LastClientPortalVersion { get; set; }

        [FirestoreProperty("selectedBusinessUnit")]
        [JsonProperty(PropertyName = "selectedBusinessUnit")]
        public string SelectedBusinessUnit { get; set; }

        #endregion
    }
}