// Migrated from Firebase to Azure Cosmos DB
// import { collection, collectionData, doc, docData, DocumentData, Firestore, query, setDoc, where } from '@angular/fire/firestore';
import { generateUuid } from '@azure/core-http';
import moment from 'moment';
import { from, Observable, take } from 'rxjs';

export class DataInterface {

  // ======= Data Interface properties and functions (migrated to Azure Cosmos DB)

  public static cosmosClient: any; // Azure Cosmos DB client

  public static setCosmosClient(cosmosClient: any) {
    this.cosmosClient = cosmosClient;
  }

  // ========================= Endpoints (migrated to Azure Cosmos DB) =========================

  // ======== User(s) ========

  public static getUser(userId: string): Observable<any> {
    // TODO: Implement Azure Cosmos DB user retrieval
    // This method needs to be updated to use Azure Cosmos DB SDK
    throw new Error('Method needs to be implemented for Azure Cosmos DB');
  }

  public static getUsers(): Observable<any> {
    // TODO: Implement Azure Cosmos DB users retrieval
    // This method needs to be updated to use Azure Cosmos DB SDK
    throw new Error('Method needs to be implemented for Azure Cosmos DB');
  }

  // ======== Business Unit(s) ========

  public static getBusinessUnit(businessUnitId: string): Observable<any> {
    // TODO: Implement Azure Cosmos DB business unit retrieval
    throw new Error('Method needs to be implemented for Azure Cosmos DB');
  }

  public static getBusinessUnits(userId) {
    // TODO: Implement Azure Cosmos DB business units retrieval
    throw new Error('Method needs to be implemented for Azure Cosmos DB');
  }

  // ======== Asset(s) ========

  public static getAsset(assetId: string): Observable<any> {
    // TODO: Implement Azure Cosmos DB asset retrieval
    throw new Error('Method needs to be implemented for Azure Cosmos DB');
  }

  public static getAssets(): Observable<any> {
    // TODO: Implement Azure Cosmos DB assets retrieval
    throw new Error('Method needs to be implemented for Azure Cosmos DB');
  }

  // ======== Work Order(s) ========

  public static getWorkOrder(workOrderId: string): Observable<any> {
    // TODO: Implement Azure Cosmos DB work order retrieval
    throw new Error('Method needs to be implemented for Azure Cosmos DB');
  }

  public static setWorkOrderField(workOrderId:string, field: string, value:string): Observable<any> {


    const workOrderRef = collection(this.firestore, "workorders");

    var changeLogFields = [
      WorkOrder.Field.JOB_SCOPE, 
      WorkOrder.Field.INSPECTION_SUMMARY, 
      WorkOrder.Field.APPLICABLE_DAMAGE_MECHANISMS,
      WorkOrder.Field.RELEVANT_INDICATIONS,
      WorkOrder.Field.RECOMMENDATIONS
    ]

    if (changeLogFields.includes(field)) {
      var uidProperty = generateUuid().toString()

      var docEntry = {
        [field]: {
          "ValueChangeLog": {
            [uidProperty]: {
              "U": "<EMAIL>",
              "T": moment().valueOf(),
              "V": value
            }
          }
        }
      }
      return from(setDoc(doc(workOrderRef, workOrderId), docEntry, { merge: true }))
    } 
  }

  // ======== Task(s) ========

  public static getTask(taskId: string): Observable<DocumentData> {
    const reference = doc(this.firestore, "tasks", taskId)
    return from (docData(reference).pipe(take(1)))
  }

  public static getTasksByWorkOrders(workOrders: string[]): Observable<any> {
    const tasksReference = collection(this.firestore, "tasks")
    const tasksQuery = query(tasksReference, where("WorkOrderId", "in", workOrders))
    return from(collectionData(tasksQuery, { idField: 'id' }).pipe(take(1)))
  }

  // Utils

  static getValue(data, property: string) {
    var reference = property + ".ValueChangeLog"
    var ref = this.get(data, reference)
    if (ref != undefined) {
      var convertedArray = Array.from(Object.values(ref))
      var sortedArray = convertedArray.sort(this.sortByTime)
      var entry = sortedArray.at(0)
      if (entry["V"] != null) {
        return entry["V"]
      }
    }
  }

  static get = function (obj, key) {
    return key.split(".").reduce(function (o, x) {
      return (typeof o == "undefined" || o === null) ? o : o[x];
    }, obj);
  }

  static sortByTime(a, b) {
    if (a.T > b.T)
      return -1;
    if (a.attr < b.attr)
      return 1;
    return 0;
  }
}

// ========================= Data Models =========================

export class User {
  effectiveBusinessUnitIds: []
  role
  constructor(data: DocumentData) {
    this.effectiveBusinessUnitIds = data["EffectiveBusinessUnitIds"]
    this.role = data["role"]
  }
}

export class BusinessUnit {
  clientId
  name
  constructor(data: DocumentData) {
    this.clientId = data["ClientId"]
    this.name = DataInterface.getValue(data, "Name")
  }
}

export class Asset {
  id
  workOrderIds
  category
  businessUnitId
  assetName
  constructor(data: DocumentData) {
    this.id = data["Id"]
    this.workOrderIds = data["WorkOrderIds"]
    this.category = data["AssetCategory"]
    this.businessUnitId = DataInterface.getValue(data, "BusinessUnitId")
    this.assetName = this.getName(data)
  }

  getName(data) {
    var assetName = ""
    var reference_510 = DataInterface.get(data, "Section510_Asset_Walkdown_Details_F.SectionIdentification.510AW_Q006.ValueChangeLog")
    var reference_570 = DataInterface.get(data, "Section570_Asset_Walkdown_Details_F.SectionIdentification.570AW_Q006.ValueChangeLog")
    var reference_653 = DataInterface.get(data, "Section653_Asset_Walkdown_Details_F.SectionIdentification.653AW_Q002.ValueChangeLog")
    var arrayUsed
    if (reference_510 != undefined) {
      arrayUsed = reference_510
    } else if (reference_570 != undefined) {
      arrayUsed = reference_570
    } else if (reference_653 != undefined) {
      arrayUsed = reference_653
    }

    if (arrayUsed != null) {
      var convertedArray = Array.from(Object.values(arrayUsed))
      var sortedArray = convertedArray.sort(DataInterface.sortByTime)
      var values = sortedArray.at(0)
      assetName = values["V"]
    }
    return assetName
  }
}

export class WorkOrder {
  assetId
  businessUnitId
  dueData
  plannedEnd
  plannedStart
  projectId

  // Inspection Information

  jobScope: string
  inspectionSummary
  applicableDamageMechanisms
  relevantIndications
  recommendations

  static Field = class {
      static JOB_SCOPE = "Job Scope"
      static INSPECTION_SUMMARY = "Inspection Summary"
      static APPLICABLE_DAMAGE_MECHANISMS = "Applicable Damage Mechanisms"
      static RELEVANT_INDICATIONS = "Relevant Indications Findings"
      static RECOMMENDATIONS = "Recommendations"
  }

  constructor(data: DocumentData) {
    this.assetId = data["AssetId"]
    this.businessUnitId = DataInterface.getValue(data, "BusinessUnitId")
    this.dueData = DataInterface.getValue(data, "Due Date")
    this.plannedEnd = DataInterface.getValue(data, "Planned End")
    this.plannedStart = DataInterface.getValue(data, "Planned Start")
    this.projectId = data["ProjectId"]

    this.jobScope = DataInterface.getValue(data, "Job Scope")
    this.inspectionSummary = DataInterface.getValue(data, "Inspection Summary")
    this.applicableDamageMechanisms = DataInterface.getValue(data, "Applicable Damage Mechanisms")
    this.relevantIndications = DataInterface.getValue(data, "Relevant Indications Findings")
    this.recommendations = DataInterface.getValue(data, "Recommendations")
  }
}

export class Task {
  id
  assetId
  assignedUsers
  businessUnitId
  clientCostCode
  clientWorkOrderNumber
  dueDate
  plannedEnd
  plannedStart
  projectId
  status
  supervisor
  taskDetails
  purchaseOrderAFE
  apmNumber
  taskType
  workOrderId
  workOrder

  constructor(data: DocumentData) {
    this.id = data["id"]
    this.assetId = data["AssetId"]
    this.assignedUsers = data["AssignedUsers"]
    this.businessUnitId = DataInterface.getValue(data, "BusinessUnitId")
    this.clientCostCode = DataInterface.getValue(data, "Client Cost Code")
    this.clientWorkOrderNumber = DataInterface.getValue(data, "Client Work Order Number")
    this.dueDate = DataInterface.getValue(data, "Due Date")
    this.plannedEnd = DataInterface.getValue(data, "Planned End")
    this.plannedStart = DataInterface.getValue(data, "Planned Start")
    this.projectId = data["ProjectId"]
    this.status = DataInterface.getValue(data, "Status")
    this.supervisor = DataInterface.getValue(data, "TaskDetails.Supervisor")
    this.purchaseOrderAFE = DataInterface.getValue(data, "Purchase Order_AFE")
    this.apmNumber = DataInterface.getValue(data, "Task APM Number")
    // task details
    this.taskType = data["TaskType"]
    this.workOrderId = data["WorkOrderId"]
  }

  //withWorkOrder(firestore: Firestore, workOrderId:string) {
  //    this.workOrder = DataInterface.getWorkOrder(firestore, workOrderId)
  //}
}