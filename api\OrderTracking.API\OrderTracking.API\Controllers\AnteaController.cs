﻿using AIMaaS.Models;
using AIMaaS.Services;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Models;
using ClientPortal.Shared.Services;
// Migrated from Google Cloud to Azure
// using Google.Cloud.Firestore;
// using Google.Cloud.Storage.V1;
using Microsoft.Azure.Cosmos;
using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using OrderTracking.API.Models;
using OrderTracking.API.Services;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace OrderTracking.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class AnteaController : ControllerBase
    {
        private readonly IAnteaService _anteaService;
        private readonly String _anteaAttachmentsBucketName;
        private readonly IUserProfilesService _userProfiles;
        private readonly FirestoreDb _firestoreDb1;
        private readonly IEmailService _emails;
        public AnteaController(IAnteaService anteaService, IUserProfilesService userProfiles, IOptions<Connections> options ,
            IEmailService emails
            )
        {
            _anteaService = anteaService;
            _userProfiles = userProfiles;
           _emails = emails;

            FirestoreDb firestoreDb = new FirestoreDbBuilder
            {
                ProjectId = options.Value.ProjectId,
                DatabaseId = options.Value.DatabaseName
            }.Build();

            _anteaAttachmentsBucketName = options.Value.AnteaAttachmentsBucketName;

            _firestoreDb = firestoreDb.Collection(options.Value.UserProfiles);
            _firestoreDb1 = firestoreDb;
        }
        private readonly CollectionReference _firestoreDb;


        [HttpGet("DownloadFile")]
        public async Task<IActionResult> DownloadFile([FromQuery] string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                return BadRequest("File path is required.");
            }

                var objectName = HttpUtility.UrlDecode(filePath);
            try
            {
                // Access Google Cloud Storage
                var bucketName = this._anteaAttachmentsBucketName;
                var storage = StorageClient.Create();
                var stream = new MemoryStream();
                await storage.DownloadObjectAsync(bucketName, objectName, stream);
                stream.Position = 0;
                byte[] bytes= stream.ToArray();
                string base64 = Convert.ToBase64String(bytes);
                return Ok( new { data = base64,filetype= GetContentTypeFromExtension(Path.GetExtension(objectName)) } );
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = "File is temporarily unavailable. Please contact your TEAM AIOM support for expedited access.\n\nDetails: " + ex.Message.ToString() });

            }
        }

        private string GetContentTypeFromExtension(string extension)
        {
            extension = extension.ToLower();

            if (extension.Contains(".pdf"))
            {
                return "application/pdf";
            }
            else if (extension.Contains(".docx"))
            {
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            }
            else if (extension.Contains(".xlsx"))
            {
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            }
            else if (extension.Contains(".png"))
            {
                return "image/png";
            }
            else if (extension.Contains(".jpg") || extension.Contains(".jpeg"))
            {
                return "image/jpeg";
            }
            else if (extension.Contains(".gif"))
            {
                return "image/gif";
            }
            else
            {
                return "application/octet-stream";
            }
        }


        [HttpPost("addPreferenceField")]
        public async Task<IActionResult> AddField([FromBody] Dictionary<string, string> requestData)
        {
            string userId = requestData.ContainsKey("userId") ? requestData["userId"] : null;
            string storageKey = requestData.ContainsKey("storageKey") ? requestData["storageKey"] : null;
            string value = requestData.ContainsKey("value") ? requestData["value"] : null;
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(storageKey) )
            {
                return BadRequest("userId, storageKey, and value are required.");
            }

            try
            {
                DocumentReference docRef = _firestoreDb.Document(userId);
                DocumentSnapshot snapshot = await docRef.GetSnapshotAsync();

                if (snapshot.Exists)
                {
                    Dictionary<string, object> update = new Dictionary<string, object>
                    {
                     { storageKey, value }
                    };
                    Dictionary<string, object> updates = new Dictionary<string, object>
                     

            {
                { $"preference", update }
            };

                    await docRef.SetAsync(updates, SetOptions.MergeAll);
                    return Ok("Preference updated successfully.");
                }
                else
                {
                    return NotFound("User document not found.");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("getPreferenceField")]
        public async Task<IActionResult> GetField([FromQuery] string userId, [FromQuery] string storageKey)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(storageKey))
            {
                return BadRequest("userId and storageKey are required.");
            }

            try
            {
                DocumentReference docRef = _firestoreDb.Document(userId);
                DocumentSnapshot snapshot = await docRef.GetSnapshotAsync();

                if (snapshot.Exists)
                {
                    if (snapshot.ContainsField("preference"))
                    {
                        var preference = snapshot.GetValue<Object>("preference");
                        return Ok(  preference);
                    }
                    return Ok();
                }
                else
                {
                    return NotFound("User document not found.");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("Assests")]
        public async Task<IActionResult> GetAssets()
        {
            try
            {

            var assests = await _anteaService.GetAssetsAsync();
            var email = User?.Identity?.Name?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
            var hasDemoRole = user.HasRole("aimaas:demo");
            if (hasDemoRole)
            {
                var sites = await _anteaService.GetAllAssetManagementSitesAsync();
                    var demoSite = sites.FirstOrDefault(site => site.LOCATIONNAME.Trim().ToUpper().Contains("DEMO SITE"));
                    if (demoSite == null) return NotFound();
                return Ok(assests.Where(component => component.LOCATIONID == demoSite.LOCATIONID));
            }
            if (!hasAdminRole)
                assests = assests.Where(asset => user.AssetManagementSiteIds.Contains(asset.LOCATIONID)).ToList();
            return Ok(assests);
            }
            catch(Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("AssetManagementSites")]
        public async Task<IActionResult> GetAllAssetManagementSites()
        {
            try
            {
                var sites = await _anteaService.GetAllAssetManagementSitesAsync();
                var email = User?.Identity?.Name?.ToLower();
                var user = await _userProfiles.GetAsync(email);
                var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
                var hasDemoRole = user.HasRole("aimaas:demo");
                if (hasDemoRole)
                {
                    var demoSite = sites.FirstOrDefault(site => site.LOCATIONNAME.Trim().ToUpper().Contains("DEMO SITE"));
                    if (demoSite == null) return NotFound();
                    return Ok(sites.Where(component => component.LOCATIONID == demoSite.LOCATIONID));
                }
                if (!hasAdminRole)
                    sites = sites.Where(asset => user.AssetManagementSiteIds.Contains(asset.LOCATIONID)).ToList();
                return Ok(sites);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("AssetAttachments")]
        public async Task<IActionResult> GetAssetAttachments(string assetid)
        {
            try
            {
                var assetAttachements = await _anteaService.GetAssetAttachmentsAsync(assetid);
                return Ok(assetAttachements);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("AssetComponents")]
        public async Task<IActionResult> GetAssetComponents(string assetId)
        {
            try
            {
                return Ok(await _anteaService.GetAssetComponentsAsync(assetId));
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("ChamberData")]
        public async Task<IActionResult> GetChamberData(string assetId)
        {
            try
            {
                return Ok(await _anteaService.GetChamberDataAsync(assetId));
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("CorrosionAnalysis")]
        public async Task<IActionResult> GetCorrosionAnalysis(string operationId)
        {
            try
            {
                return Ok(await _anteaService.GetCorrosionAnalysisAsync(operationId));
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("GeneralAnalysis")]
        public async Task<IActionResult> GetGeneralAnalysis([FromQuery] string assetId)
        {
            try
            {

                return Ok(await _anteaService.GetGeneralAnalysis(assetId));
            }
            catch(Exception e) {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("EquipmentData")]
        public async Task<IActionResult> GetEquipments()
        {
            try
            {
               
                return Ok( _anteaService.GetEquipmentData());
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("Inspections")]
        public async Task<IActionResult> GetInspections()
        {
            try
            {
                var inspections = await _anteaService.GetInspectionsAsync();
                var email = User?.Identity?.Name?.ToLower();
                var user = await _userProfiles.GetAsync(email);
                var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
                var hasDemoRole = user.HasRole("aimaas:demo");
                if (hasDemoRole)
                {
                    var sites = await _anteaService.GetAllAssetManagementSitesAsync();
                    var demoSite = sites.FirstOrDefault(site => site.LOCATIONNAME.Trim().ToUpper().Contains("DEMO SITE"));
                    if (demoSite == null) return NotFound();
                    return Ok(inspections.Where(component => component.LOCATIONID == demoSite.LOCATIONID));
                }
                if (!hasAdminRole)
                    inspections = inspections.Where(asset => user.AssetManagementSiteIds.Contains(asset.LOCATIONID)).ToList();
                return Ok(inspections);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("InspectionsAttachments")]
        public async Task<IActionResult> GetInspectionAttachments(string operationid)
        {
            try
            {
                var inspectionAttachements = await _anteaService.GetInspectionAttachmentsAsync(operationid);
                return Ok(inspectionAttachements);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
        [HttpGet("Anomalies")]
        public async Task<IActionResult> GetAnamolies()
        {
            try
            {
                var anomalies = await _anteaService.GetAnomaliesAsync();
                var email = User?.Identity?.Name?.ToLower();
                var user = await _userProfiles.GetAsync(email);
                var hasAdminRole = user.HasRole("aimaas:admin") || user.HasRole("app:admin");
                var hasDemoRole = user.HasRole("aimaas:demo");
                if (hasDemoRole)
                {
                    var sites = await _anteaService.GetAllAssetManagementSitesAsync();
                    var demoSite = sites.FirstOrDefault(site => site.LOCATIONNAME.Trim().ToUpper().Contains("DEMO SITE"));
                    if (demoSite == null) return NotFound();
                    return Ok(anomalies.Where(component => component.LOCATIONID == demoSite.LOCATIONID));
                }
                if (!hasAdminRole)
                    anomalies = anomalies.Where(asset => user.AssetManagementSiteIds.Contains(asset.LOCATIONID)).ToList();
                return Ok(anomalies);
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpPost("UploadFilesToGCPBucket")]
        public async Task<IActionResult> UploadFilesToGCPBucket([FromBody] SubmissionFileUpload submissionFile)
        {

            if (await _anteaService.UploadSubmissionFilesToGCPBucket(submissionFile))
                return Ok("FileUploaded successfully");
            else
                return BadRequest("Error while uploading file");
        }
        [HttpPost("Submissions")]
        public async Task<IActionResult> PostSubmissions([FromBody] Submissions submissions)
        {
            try
            {
                var _submissionsCollection = _firestoreDb1.Collection("OIS-Submissions");
                Guid Id = Guid.NewGuid();
                submissions.Id = Id.ToString();
                var _fireStorAssetsInfo = new List<FireStoreSubmissionAssetInfo>();
                var _fireStoreAnomalyInfo = new FireStoreSubmissionAnomalyInfo();
                if (submissions.AssetInfo != null && submissions.AssetInfo?.Count !=0)
                {
                    foreach (var asset in submissions.AssetInfo)
                    { 
                        _fireStorAssetsInfo.Add( new FireStoreSubmissionAssetInfo
                        {
                            AssetName = asset?.AssetName,
                            AssetDescription = asset?.AssetDescription,
                            AssetId = asset?.AssetId,
                        });
                    }
                    
                }
                if (submissions.LocationId != null)
                {
                    submissions.CostCenter = await _anteaService.GetCostCenter(submissions.LocationId);


                }
                if (submissions.AnomalyInfo != null)
                {

                    _fireStoreAnomalyInfo = new FireStoreSubmissionAnomalyInfo
                    {
                        AnomalyId = submissions.AnomalyInfo?.AnomalyNumber,
                        AnomalyNumber = submissions.AnomalyInfo?.AnomalyNumber,
                        AnomalyPriority = submissions.AnomalyInfo?.AnomalyPriority,
                        AnomalyType = submissions.AnomalyInfo?.AnomalyType,
                        AnomalyInspectionOperation= submissions.AnomalyInfo?.AnomalyInspectionOperation
                    };

                }

                var _firestoreSubmissions = new FireStoreSubmissions
                {
                    Id = Id.ToString(),
                    ClientFacility=submissions.ClientFacility,
                    SubmissionType = submissions.SubmissionType,
                    AnomalyInfo = _fireStoreAnomalyInfo,
                    ClientClosedDate = submissions.ClientClosedDate,
                    Comment = submissions.Comment,
                    AssetInfo = _fireStorAssetsInfo,
                    ServiceType = submissions.ServiceType,
                    CreatedDate = submissions.CreatedDate,
                    SubmittedUser = submissions.SenderInfo.GivenName+" "+submissions.SenderInfo.Surname,
                    CostCenter = submissions.CostCenter

                };
                    List<FireStoreSubmissionFileUpload> _firestoreDocuments = new List<FireStoreSubmissionFileUpload>();
                if (submissions.Documents != null)
                {

                    foreach (var doc in submissions.Documents)
                    {
                        var randomGuid = Guid.NewGuid();
                        var _doc = new SubmissionFileUpload()
                        {
                            FileData = doc.FileData,
                            FileType = doc.FileType,
                            FileName = (randomGuid.ToString() + '_' + doc.FileName)
                        };
                        if (await _anteaService.UploadSubmissionFilesToGCPBucket(_doc))
                        {
                            _firestoreDocuments.Add(new FireStoreSubmissionFileUpload
                            {
                                FileName = _doc.FileName,
                                FileType = _doc.FileType
                            });
                        }
                    }
                    _firestoreSubmissions.Documents = _firestoreDocuments;
                }
                var _submission = _submissionsCollection.Document(_firestoreSubmissions.Id);
                await _submission.SetAsync(_firestoreSubmissions);
                var emailResult = await _anteaService.SendSubmissionMail(submissions, _firestoreDocuments, _emails);
                if (emailResult == "Success")
                {
                    return Ok(new
                    {
                        message = "Submission submitted successfully",
                        result = "Success"
                    });
                }
                else
                {
                    return Ok(new
                    {
                        message = "Submission submitted successfully but there is problem while sending email. Error Message::" + emailResult,
                        result = "Success"
                    });
                ;
                }
            }
            catch (Exception e)
            {
                return BadRequest("Error:: " + e.Message);
            }

        }

        [HttpGet("Submissions")]
        public async Task<IActionResult> Submissions(string assetid)
        {
            try
            {
                return Ok(await _anteaService.GetSubmissionsAsync(assetid));
            }
            catch (Exception e)
            {
                return BadRequest("Error:: " + e.ToString());
            }
        }
        [HttpDelete("Submissions")]
        public async Task<IActionResult> DeleteSubmission(string id)
        {
            try
            {
                // Validate the id parameter
                if (string.IsNullOrWhiteSpace(id))
                {
                    return BadRequest(new
                    {
                        message = "Error: Submission ID cannot be null, empty, or whitespace.",
                        result = "Failure"
                    });
                }

                // Get the reference to the Firestore collection
                var _submissionsCollection = _firestoreDb1.Collection("OIS-Submissions");

                // Reference the document by its ID
                var submissionDoc = _submissionsCollection.Document(id);

                // Check if the document exists
                var docSnapshot = await submissionDoc.GetSnapshotAsync();
                if (docSnapshot.Exists)
                {
                    // Document exists, so delete it
                    await submissionDoc.DeleteAsync();
                    return Ok(new
                    {
                        message = "Submission deleted successfully",
                        result = "Success"
                    });
                }
                else
                {
                    // Document doesn't exist
                    return NotFound(new
                    {
                        message = "Submission not found",
                        result = "Failure"
                    });
                }
            }
            catch (Exception e)
            {
                return BadRequest(new
                {
                    message = "Error: " + e.Message,
                    result = "Failure"
                });
            }

        }
        [HttpGet("SystemManagementCategories")]
        public async Task<IActionResult> SystemManagementCategories()
        {
            try
            {
                return Ok(await _anteaService.GetSystemManagementCategories());
            }
            catch (Exception e)
            {
                return BadRequest("Error:: " + e.ToString());
            }
        }
    }
}
