﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OrderTracking.API.Services;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    ///     API Controller to manage roles that can be applied to User Profiles
    /// </summary>
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    [Authorize(Policy = "UserIsActive")]
    [Authorize(Policy = "UserIsModuleAdmin")]
    public class RolesController : ControllerBase
    {
        #region Constructors

        /// <summary>
        ///     Constructor that injects an IRolesService, IUserProfilesService, and a logging factory.
        /// </summary>
        /// <param name="rolesCosmosService"></param>
        /// <param name="userProfileService"></param>
        /// <param name="loggerFactory"></param>
        public RolesController(IRolesService rolesCosmosService, IUserProfilesService userProfileService,
            ILoggerFactory loggerFactory)
        {
            _log = loggerFactory.CreateLogger<RolesController>();
            _rolesService = rolesCosmosService;
            _userProfileService = userProfileService;
        }

        #endregion

        #region Fields and Constants

        private readonly ILogger _log;
        private readonly IRolesService _rolesService;
        private readonly IUserProfilesService _userProfileService;

        #endregion

        #region Public Methods

        /// <summary>
        ///     Adds the specified user to the specified Role.
        /// </summary>
        /// <param name="roleKey"></param>
        /// <param name="userKey"></param>
        /// <returns></returns>
        [HttpPut("{roleKey}/users/{userKey}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> AddUserToRole([FromRoute] string roleKey, [FromRoute] string userKey)
        {
            var adminUser = await _userProfileService.GetAsync(User.FindFirst("emails").Value);
           
            // TODO: Refactor to an Authorization Policy Handler
            var assignableRoles = adminUser.AssignableRoles(roleKey);
            if (!assignableRoles.Any())
                return Forbid();
            var user = await _userProfileService.GetAsync(userKey);
            if (!user.HasWorkEmail())
                return Forbid();
            // TODO: Refactor to an Authorization Policy Handler ^


            var error =  UserProfilesService.CanRolesBeAdded(user, new string[]{roleKey}, Array.Empty<string>());
            if (error.Error)
            {
                return Unauthorized(error.Message);
            }

            await _userProfileService.AddRoleToUser(userKey, roleKey );
            return NoContent();
        }

        /// <summary>
        ///     Delete a role.  Should only succeed if no users are assigned that role.
        /// </summary>
        /// <param name="roleKey"></param>
        /// <returns></returns>
        [Authorize("App:Admin")]
        [HttpDelete("{roleKey}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<ActionResult> Delete([FromRoute] string roleKey)
        {
            var users = await _userProfileService.GetUsersForRoleAsync(roleKey);
            if (users.Any()) return BadRequest($"{roleKey} role still has users associated with it.  Cannot delete.");
            await _rolesService.RemoveAsync(roleKey);
            return NoContent();
        }

        /// <summary>
        ///     Delete multiple roles (for QA purposes)
        /// </summary>
        /// <param name="roles"></param>
        /// <returns></returns>
        [Authorize(Policy = "App:QA")]
        [HttpDelete]
        public async Task<IActionResult> DeleteRoles(string[] roles)
        {
            if (roles == null) throw new ArgumentNullException(nameof(roles));

            foreach (var role in roles)
            {
                var users = await _userProfileService.GetUsersForRoleAsync(role);
                if (users.Any())
                    return BadRequest($"{role} role still has users associated with it.  Cannot delete.");
                await _rolesService.RemoveAsync(role);
            }

            return NoContent();
        }

        /// <summary>
        ///     Retrieve list of defined Roles
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Role>>> Get()
        {
            var roles = await _rolesService.GetAllAsync();
            // Check a users admin groups
            var adminUser = await _userProfileService.GetAsync(User.FindFirst("emails").Value.ToLower());
            return adminUser.AssignableRoles(roles).ToList();
        }

        /// <summary>
        ///     Get a specific Role
        /// </summary>
        /// <param name="roleKey"></param>
        /// <returns></returns>
        [HttpGet("{roleKey}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<Role>> Get([FromRoute] string roleKey)
        {
            var role = await _rolesService.GetAsync(roleKey);
            return role;
        }

        /// <summary>
        ///     Retrieve list of defined Groups
        /// </summary>
        /// <returns></returns>
        [HttpGet("groups")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<string>>> GetGroups()
        {
            var groups = await _rolesService.GetGroupsAsync();
            return groups.ToList();
        }

        /// <summary>
        ///     Retrieve list of Users who have the specified Role ("e.g, "WMR:view")
        /// </summary>
        /// <returns>OK and the Users having the specified Role (even if there are no Users matching the specified Role).</returns>
        [HttpGet("{role}/users")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IEnumerable<UserProfile>> GetUsers([FromRoute] string role)
        {
            var users = await _userProfileService.GetUsersForRoleAsync(role);
            return users.ToList().OrderBy(u => u.Name);
        }

        /// <summary>
        ///     Retrieve list of Users who have at least one Role (e.g., "WMR:view") from the specified Group (e.g,. "WMR")
        /// </summary>
        /// <returns>OK and the Users having the specified Group (even if there are no Users matching the specified Group).</returns>
        [HttpGet("groups/{group}/users")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IEnumerable<UserProfile>> GetUsersFromGroup([FromRoute] string group)
        {
            var users = await _userProfileService.GetUsersForGroupAsync(group);
            return users.ToList().OrderBy(u => u.Name);
        }

        /// <summary>
        ///     Create a Role.
        /// </summary>
        /// <param name="role"></param>
        /// <returns></returns>
        [Authorize("App:Admin")]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        public async Task<ActionResult<IEnumerable<Role>>> Post([FromBody] Role role)
        {
            var existingRole = await _rolesService.GetAsync(role.Id);
            if (existingRole != null)
                return Conflict();
            var newRole = await _rolesService.AddAsync(role);
            if (newRole != null)
                return CreatedAtAction(nameof(Get), new {roleKey = newRole.RoleKey}, newRole);
            return Conflict();
        }

        /// <summary>
        ///     Update a Role
        /// </summary>
        /// <param name="role"></param>
        /// <param name="originalId"></param>
        /// <returns></returns>
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<Role>> Put([FromBody] Role role, [FromQuery] string originalId = null)
        {
            if (role == null) throw new ArgumentNullException(nameof(role));
            if (string.IsNullOrEmpty(role.Id)) return NotFound();
            
            var adminUser = await _userProfileService.GetAsync(User.FindFirst("emails").Value);
            var assignableRoles = adminUser.AssignableRoles(role);
            if (!assignableRoles.Any())
            {
                return Forbid();
            }
            var updated = await _rolesService.UpdateAsync(role, originalId);
            if (updated != null)
                return updated;
            return BadRequest(); //Either NotFound or Conflict // JDS: um... BadRequest is neither NotFound nor Conflict
        }

        /// <summary>
        ///     Removes the specified Role from the specified User.  Will return OK (200) regardless of whether
        ///     the specified Role actually existed for the User.  I.e., will NOT return NotFound (404) if the Role is not in the
        ///     UserProfile.roles array.
        /// </summary>
        /// <param name="role"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpDelete("{role}/users/{user}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<ActionResult> RemoveUserFromRole([FromRoute] string role, [FromRoute] string user)
        {

            var adminUser = await _userProfileService.GetAsync(User.FindFirst("emails").Value);
            var assignableRoles = adminUser.AssignableRoles(role);
            if (!assignableRoles.Any())
            {
                return Forbid();
            }

            var userProfile = await _userProfileService.GetAsync(user, user);
            if (userProfile == null) return NotFound();

            if (userProfile.Roles.Remove(role)) await _userProfileService.UpdateAsync(userProfile);
            return NoContent();
        }

        #endregion
    }
}