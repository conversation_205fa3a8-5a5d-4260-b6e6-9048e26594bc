﻿using Azure.Storage.Blobs.Models;
using Microsoft.AspNetCore.Http;
using OrderTracking.API.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderTracking.API.Interfaces.Services
{
    /// <summary>
    /// Cloud Storage Service Type
    /// </summary>
    public interface ICloudStorageService
    {
        /// <summary>
        /// Get signed url for accessing objects of the bucket
        /// </summary>
        /// <returns></returns>
        Task<string> GetSignedUrl();

        /// <summary>
        /// Get signed url for accessing a specific object in a bucket
        /// </summary>
        /// <param name="objectName"></param>
        /// <returns></returns>
        Task<string> GetSignedUrl(string objectName);

        /// <summary>
        /// deletes objects from the buckets
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        Task DeleteObjectAsync(string folderName, string objectName);

        /// <summary>
        /// downloads object from the bucket
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        Task<CloudStorageDownloadedObject> DownloadObjectAsync(string folderName, string objectName);

        /// <summary>
        /// List objects from the buckets
        /// </summary>
        /// <param name="bucketName"></param>
        /// <returns></returns>
        Task<IEnumerable<Google.Apis.Storage.v1.Data.Object>> ListObjectAsync(string folderName);

        /// <summary>
        /// Uploads objects to the bucket
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="stream"></param>
        /// <param name="uploadedByEmail"></param>
        /// <returns></returns>....
        Task<Google.Apis.Storage.v1.Data.Object> UploadAttachmentObjectAsync(string folderName, IFormFile stream);
    }
}
