import { AssetCategory } from '../apm-asset-category';

export type ListChangeLog = {
    [key: string]: {
        A: 'Added' | 'Removed';
        T: number;
        U: string;
        V: string;
    };
};

export type FirebaseUser = {
    id: string;
    BusinessUnitIds: FirebaseListAttribute<string>;
    EffectiveBusinessUnitIds: string[];
    Name: FirebaseAttribute<string>;
};

export type ProjectActivityItem = {
    Count: FirebaseAttribute<number>;
    Duration: FirebaseAttribute<number>;
};

export type ProjectActivityTrackerValue = {
    Permitting?: ProjectActivityItem;
    'Job Setup'?: ProjectActivityItem;
    Lunch?: ProjectActivityItem;
    'Post CleanUp'?: ProjectActivityItem;
    'FW-RT'?: ProjectActivityItem;
    'FW-MT'?: ProjectActivityItem;
    'FW-PT'?: ProjectActivityItem;
    'FW-UT'?: ProjectActivityItem;
    'FW-VT'?: ProjectActivityItem;
    'FW-ML'?: ProjectActivityItem;
    'FW-GW'?: ProjectActivityItem;
    'FW-ET'?: ProjectActivityItem;
    'FW-LS'?: ProjectActivityItem;
    'FW-GPR'?: ProjectActivityItem;
    'FW-LT'?: ProjectActivityItem;
    'FW-IR'?: ProjectActivityItem;
    'FW-PMI'?: ProjectActivityItem;
    'FW-AE'?: ProjectActivityItem;
    'FW-VA'?: ProjectActivityItem;
    'FW-API 510'?: ProjectActivityItem;
    'FW-API 570'?: ProjectActivityItem;
    'FW-API 653'?: ProjectActivityItem;
    'FW-AWS CWI'?: ProjectActivityItem;
    'FW-NACE'?: ProjectActivityItem;
    'FW-Other'?: ProjectActivityItem;
    'Client WO Number': FirebaseAttribute<string>;
    Date: FirebaseAttribute<string>;
    User: FirebaseAttribute<string>;
    id: string;
};

export interface FirebaseProject {
    id: string;
    AccountingDetails: {
        'APM Project Number': FirebaseAttribute<string>;
        'Project Number': FirebaseAttribute<string>;
        'Team District Number': FirebaseAttribute<string>;
        'TEAM Project Number': FirebaseAttribute<string>;
    };
    // TODO: ClientDetails
    AssetIds: FirebaseListAttribute<string>;
    BusinessUnitId: FirebaseAttribute<string>;
    LocationId: string;
    Name: FirebaseAttribute<string>;
    'Planned End': FirebaseAttribute<string>;
    'Planned Start': FirebaseAttribute<string>;
    ProjectActivities: {
        Value: string;
        ValueChangeLog: ListChangeLog;
        Values: {
            [key: string]: ProjectActivityTrackerValue;
        };
    };
}

export interface FirebaseAsset {
    id: string;
    BusinessUnitId: FirebaseAttribute<string>;
    ProjectIds: string[];
    AssetCategory: AssetCategory;
    Section510_Asset_Walkdown_Details_F?: unknown; // TODO:
    Section570_Asset_Walkdown_Details_F?: unknown; // TODO:
    Section653_Asset_Walkdown_Details_F?: unknown; // TODO:
    Area: FirebaseAttribute<string>;
    Unit: FirebaseAttribute<string>;
}

export type TaskActivityTrackerValue = {
    Date: FirebaseAttribute<string>;
    Permitting: FirebaseAttribute<number>;
    'Job Setup': FirebaseAttribute<number>;
    Lunch: FirebaseAttribute<number>;
    'Post CleanUp': FirebaseAttribute<number>;
    'FW-VT': FirebaseAttribute<number>;
    'FW-API 510': FirebaseAttribute<number>;
    'FW-API 570': FirebaseAttribute<number>;
    'FW-API 653': FirebaseAttribute<number>;
    'FW-Other': FirebaseAttribute<number>;
    id: string;
};

export interface FirebaseTask {
    id: string;
    AssetId: string;
    AssignedUsers: string[];
    BusinessUnitId: FirebaseAttribute<string>;
    'Client Work Order Number': FirebaseAttribute<string>;
    ProjectId: string;
    Status: FirebaseAttribute<string>;
    'Task APM Number': FirebaseAttribute<string>;
    TaskType: string;
    WorkOrderId: string;
    'Due Date': FirebaseAttribute<string>;
    Supervisor: FirebaseAttribute<string>;
    'Planned Start': FirebaseAttribute<string>;
    'Planned End': FirebaseAttribute<string>;
    'Activity Tracker': {
        Value: string;
        ValueChangeLog: ListChangeLog;
        Values: {
            [key: string]: TaskActivityTrackerValue;
        };
    };
}

export interface FirebaseWorkOrder {
    id: string;
    'APM Work Order Number': FirebaseAttribute<string>;
    AssetId: string;
    BusinessUnitId: FirebaseAttribute<string>;
    'Due Date': FirebaseAttribute<string>;
    ProjectId: string;
    'Planned Start': FirebaseAttribute<string>;
    'Planned End': FirebaseAttribute<string>;
    'Facility Name': FirebaseAttribute<string>;
    Status: FirebaseAttribute<string>;
    AssignedUsers: string[];
    'GIS Location': FirebaseLocationAttribute;
    'Primary Contact Name': FirebaseAttribute<string>;
    'Primary Contact Phone': FirebaseAttribute<string>;
    'Job Scope': FirebaseAttribute<string>;
    'Inspection Summary': FirebaseAttribute<string>;
    'Applicable Damage Mechanisms': FirebaseAttribute<string>;

    'Field Work Completed': FirebaseAttribute<string>;
    Recommendations: FirebaseAttribute<string>;
    'Relevent Indications Findings': FirebaseAttribute<string>;
}

export class FirebaseAttribute<T> {
    Value?: T;
    ValueChangeLog: {
        [key: string]: { T: number; U: string; V: T };
    };
}

export class FirebaseLocationAttribute extends FirebaseAttribute<string> {}

export class FirebaseListAttribute<T> {
    Value?: T[];
    ValueChangeLog: ListChangeLog;
}
